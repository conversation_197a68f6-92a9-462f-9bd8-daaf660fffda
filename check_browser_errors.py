from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.options import Options
import time
import json

# Chrome-Optionen konfigurieren
chrome_options = Options()
chrome_options.add_argument("--headless")  # Headless-Modus (ohne GUI)
chrome_options.add_argument("--window-size=1920,1080")  # Fenstergröße
chrome_options.add_argument("--log-level=3")  # Minimale Logging-Stufe

# Browser starten
driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)

try:
    # Seite öffnen
    print("Öffne die Seite...")
    driver.get("http://localhost:3000/events/airabiihd6vg9ym0prykluzx/documents/9")
    
    # Warten, bis die Seite geladen ist
    time.sleep(5)
    
    # Konsolenfehler abrufen
    print("\nKonsolenfehler:")
    logs = driver.get_log('browser')
    for log in logs:
        print(json.dumps(log, indent=2))
    
    if not logs:
        print("<PERSON><PERSON> in der Browserkonsole gefunden.")
    
    # Screenshot machen
    driver.save_screenshot("screenshot.png")
    print("\nScreenshot gespeichert als 'screenshot.png'")
    
except Exception as e:
    print(f"Fehler: {e}")
finally:
    # Browser schließen
    driver.quit()
