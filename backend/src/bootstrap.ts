export default async () => {
  // Set permissions for custom endpoints
  await setCustomPermissions();
};

async function setCustomPermissions() {
  try {
    // Find the ID of the public role
    const publicRole = await strapi.query('plugin::users-permissions.role').findOne({
      where: {
        type: 'public',
      },
    });

    if (!publicRole) {
      console.log('Public role not found');
      return;
    }

    // Check if permission already exists
    const existingPermission = await strapi.query('plugin::users-permissions.permission').findOne({
      where: {
        action: 'api::event.event.addDocument',
        role: publicRole.id,
      },
    });

    if (existingPermission) {
      console.log('Custom permission already exists');
      return;
    }

    // Create permission for the custom addDocument endpoint
    await strapi.query('plugin::users-permissions.permission').create({
      data: {
        action: 'api::event.event.addDocument',
        role: publicRole.id,
      },
    });

    // Create permission for the custom publishEvent endpoint
    const existingPublishPermission = await strapi.query('plugin::users-permissions.permission').findOne({
      where: {
        action: 'api::event.event.publishEvent',
        role: publicRole.id,
      },
    });

    if (!existingPublishPermission) {
      await strapi.query('plugin::users-permissions.permission').create({
        data: {
          action: 'api::event.event.publishEvent',
          role: publicRole.id,
        },
      });
      console.log('Custom permission created for publishEvent endpoint');
    }

    console.log('Custom permission created for addDocument endpoint');
  } catch (error) {
    console.error('Error setting custom permissions:', error);
  }
}
