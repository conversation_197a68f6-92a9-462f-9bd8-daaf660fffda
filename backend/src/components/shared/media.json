{"collectionName": "components_shared_media", "info": {"displayName": "MediaWithStuff", "icon": "picture", "description": ""}, "options": {}, "attributes": {"file": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos"]}, "messages": {"type": "relation", "relation": "oneToMany", "target": "api::message.message"}, "name": {"type": "string"}, "converted_file": {"allowedTypes": ["images", "files", "videos", "audios"], "type": "media", "multiple": false}}}