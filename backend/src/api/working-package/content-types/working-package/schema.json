{"kind": "collectionType", "collectionName": "working_packages", "info": {"singularName": "working-package", "pluralName": "working-packages", "displayName": "Working Package", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"name": {"type": "string", "required": true}, "users_permissions_users": {"type": "relation", "relation": "manyToMany", "target": "plugin::users-permissions.user", "inversedBy": "working_packages"}, "events": {"type": "relation", "relation": "manyToMany", "target": "api::event.event", "mappedBy": "working_packages"}, "documents": {"type": "component", "repeatable": true, "component": "shared.media"}}}