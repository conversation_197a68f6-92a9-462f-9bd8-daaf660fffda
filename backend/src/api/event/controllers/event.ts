/**
 * event controller
 */

import { factories } from '@strapi/strapi'

export default factories.createCoreController('api::event.event', ({ strapi }) => ({
  // Extend the default controller with custom methods
  ...factories.createCoreController('api::event.event'),

  /**
   * Custom endpoint to add a document to an event
   * POST /api/events/:documentId/add-document
   */
  async addDocument(ctx) {
    try {
      const { documentId } = ctx.params;
      const { name } = ctx.request.body;
      const files = ctx.request.files;

      // Validate input
      if (!name) {
        return ctx.badRequest('Document name is required');
      }

      if (!files || !files.file) {
        return ctx.badRequest('File is required');
      }

      const file = Array.isArray(files.file) ? files.file[0] : files.file;

      // Upload the file using Strapi's upload service
      const uploadedFiles = await strapi
        .plugin('upload')
        .service('upload')
        .upload({
          files: file,
          data: {
            fileInfo: {
              name: name,
              alternativeText: name,
              caption: name,
            },
          },
        });

      const uploadedFile = uploadedFiles[0];

      // Get current event with documents
      const currentEvent = await strapi.documents('api::event.event').findOne({
        documentId: documentId,
        populate: {
          documents: {
            populate: {
              file: true,
              converted_file: true
            }
          }
        }
      });

      if (!currentEvent) {
        return ctx.notFound('Event not found');
      }

      // Prepare existing documents - keep existing components as they are
      const existingDocuments = currentEvent.documents || [];

      // Add new document as a new component (don't include id for new components)
      const documentsData = [
        ...existingDocuments,
        {
          name: name,
          file: uploadedFile.id,
        }
      ];

      // Update the event atomically
      const updatedEvent = await strapi.documents('api::event.event').update({
        documentId: documentId,
        data: {
          documents: documentsData
        },
        populate: {
          documents: {
            populate: {
              file: true,
              converted_file: true
            }
          }
        }
      });

      // Return the updated event
      ctx.body = {
        data: updatedEvent,
        message: 'Document added successfully'
      };

    } catch (error) {
      strapi.log.error('Error adding document to event:', error);
      ctx.internalServerError('Failed to add document to event');
    }
  },

  /**
   * Custom endpoint to publish an event
   * POST /api/events/:documentId/publish
   */
  async publishEvent(ctx) {
    try {
      const { documentId } = ctx.params;

      // Get current event
      const currentEvent = await strapi.documents('api::event.event').findOne({
        documentId: documentId,
      });

      if (!currentEvent) {
        return ctx.notFound('Event not found');
      }

      if (currentEvent.publishedAt) {
        return ctx.badRequest('Event is already published');
      }

      // Publish the event using the update method with publishedAt
      const publishedEvent = await strapi.documents('api::event.event').update({
        documentId: documentId,
        data: {
          publishedAt: new Date().toISOString()
        },
        populate: {
          documents: {
            populate: {
              file: true,
              converted_file: true
            }
          }
        }
      });

      ctx.body = {
        data: publishedEvent,
        message: 'Event published successfully'
      };

    } catch (error) {
      strapi.log.error('Error publishing event:', error);
      ctx.internalServerError('Failed to publish event');
    }
  },
}));
