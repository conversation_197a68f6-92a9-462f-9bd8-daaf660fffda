/**
 * Custom event routes
 */

export default {
  routes: [
    {
      method: 'POST',
      path: '/events/:documentId/add-document',
      handler: 'event.addDocument',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/events/:documentId/publish',
      handler: 'event.publishEvent',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
