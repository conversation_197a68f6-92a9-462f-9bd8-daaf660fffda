{"kind": "collectionType", "collectionName": "events", "info": {"singularName": "event", "pluralName": "events", "displayName": "Event", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"name": {"type": "string", "required": true}, "type": {"type": "enumeration", "enum": ["Publication", "Conference"], "required": true, "default": "Publication"}, "working_packages": {"type": "relation", "relation": "manyToMany", "target": "api::working-package.working-package", "inversedBy": "events"}, "statuses": {"type": "relation", "relation": "oneToMany", "target": "api::status.status"}, "start": {"type": "datetime"}, "end": {"type": "datetime"}, "messages": {"type": "relation", "relation": "manyToMany", "target": "api::message.message", "inversedBy": "events"}, "documents": {"type": "component", "repeatable": true, "component": "shared.media"}}}