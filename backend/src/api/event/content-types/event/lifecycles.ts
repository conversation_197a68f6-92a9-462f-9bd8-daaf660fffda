import { Event as StrapiEvent } from "@strapi/database/dist/lifecycles/types";
//import { Event as EventModel } from "../../../../../../frontend/src/types/auto_generated/event";
import { Event as EventModel } from "../../../../../types/auto_generated/event";
import { ContentType } from "@strapi/types/dist/uid";
import { toArray, uniq } from "lodash";
import path from "path";
import { LibreOfficeFileConverter } from "libreoffice-file-converter";
import promises from "fs/promises";
import { createReadStream } from "node:fs";

const sendEmails = async (event: StrapiEvent) => {


  if (!event.result.publishedAt) {
    return;
  }

  const data: EventModel = {
    ...event.params.data,
    ...event.params.populate
  };

  const id = event.result.id;

  // TODO: hier mal strapi client probieren, weil publishedAt immer null ist
  const newEntry  = await strapi.entityService.findOne(
    event.model.uid as ContentType,
    id,
    {
      // @ts-ignore
      populate: {
        // @ts-ignore
        working_packages: {
          populate: {
            users_permissions_users: true
          }
        }
      }
    }
  ) as EventModel

  // @ts-ignore
  const neEntry2 = await strapi.documents(event.model.uid).findOne({
    documentId: event.result.documentId,
    status: "published",
    populate: {
      // @ts-ignore
      working_packages: {
        populate: {
          users_permissions_users: true
        }
      }
    }
  });

  console.log("Event created", {
    event, data, newEntry, neEntry2
  });

  let emails: string[] = [];

  // Sicherstellen, dass newEntry und working_packages existieren
  if (newEntry && newEntry.working_packages) {
    toArray(newEntry.working_packages).forEach(wpackage => {
    // keine ahnung, hier ist publishedAt immer null. geht also nicht wirklich :)
    // TODO: mal strapi client probieren. dann mit den types aus dem frontend
    // oder so ein strapi client package bauen? wo die types drin sind? aber muss trotzdem von beiden root foldern erreichbar sein...
    //if (wpackage.publishedAt) {


      toArray(wpackage.users_permissions_users).forEach(user => {
        if (user.email && !user.blocked && user.confirmed) {
          emails.push(user.email);
        }
      })

    //}
    })
  }

  emails = uniq(emails);

  // filter only for zeitgleich emails for now

  emails = emails.filter(email => {
    return email.includes("@zeitgleich.de");
  });


  console.log("got user emails", {
    emails
  })

  emails = [];

  let templateReferenceId =  1;

  try{

    const emailPlugin = strapi.plugins["email-designer-5"].services.email

    await Promise.all(emails.map(email => {
      debugger;
      return emailPlugin.sendTemplatedEmail(
        {
          // required
          // This can also be an array of email addresses
          to: email,
          // Optional
          //cc: ["<EMAIL>", "<EMAIL>"],
          // Optional
          //bcc: ["<EMAIL>"],
          // optional if /config/plugins.js -> email.settings.defaultFrom is set
          //from: "<EMAIL>",
          from: '<EMAIL>',
          // optional if /config/plugins.js -> email.settings.defaultReplyTo is set
          //replyTo: "<EMAIL>",
          // optional array of files
          //attachments: [],
        },
        {
          // required - Ref ID defined in the template designer (won't change on import)
          templateReferenceId,
          // If provided here will override the template's subject.
          // Can include variables like `Thank you for your order {{= USER.firstName }}!`
          //subject: `Thank you for your order`,
          subject: `Neues Event in Planung ${newEntry.name}`
        },
        {
          // this object must include all variables you're using in your email template
          USER: { firstName: "John", lastName: "Doe" },
          order: {
            products: [
              { name: "Article 1", price: 9.99 },
              { name: "Article 2", price: 5.55 },
            ],
          },
          shippingCost: 5,
          total: 20.54,
          newEntry,
          editLink: "test my edit link"
        }
      );
    }))
  } catch(err) {
    debugger;
    // make an error toast in the admin panel
    console.log(err);
    strapi.log.alert(err);
    strapi.log.error(err);
  }
}

const convertUploadedFiles = async (event: StrapiEvent) => {

  if (!event.result.publishedAt) {
    return;
  }

  const id = event.result.id;

  const newEntry  = await strapi.entityService.findOne(
    event.model.uid as ContentType,
    id,
    {
      // @ts-ignore
      populate: {
        // @ts-ignore
        working_packages: {
          populate: {
            users_permissions_users: true
          }
        },
        documents: {
          fields: ["id", "name"],
          populate: {
            file: true,
            converted_file: true
          }
        }
      }
    }
  ) as EventModel

  const files = toArray(newEntry.documents).filter(doc => {
    if (!doc.file) {
      return false;
    }
    // document has been converted already
    if (doc.converted_file) {
      return false;
    }
    // only non pdf files
    if (!doc.file.mime.includes("application/")) {
      if (doc.file.mime.includes("image/")) {
        return true;
      }
      return false;
    }
    if (doc.file.mime === "application/pdf") {
      return false;
    }
    return true;
  });

  // If no files need conversion, return early
  if (files.length === 0) {
    return;
  }

  const ext = '.pdf'
  const dir = strapi.dirs.static.public;
  const host = strapi.config.get('server.host', 'localhost');
  const port = strapi.config.get('server.port', 1337);
  const protocol = strapi.config.get('server.https.enabled', false) ? 'https' : 'http';
  const baseUrl = `${protocol}://${host}:${port}`;

  const libreOfficeFileConverter = new LibreOfficeFileConverter({
    childProcessOptions: {
      timeout: 60 * 1000,
      windowsHide: true
    },
  });

  // Make sure the converted directory exists
  const convertedDir = path.join(dir, "converted");
  try {
    await promises.mkdir(convertedDir, { recursive: true });
  } catch (err) {
    console.log("Directory already exists or could not be created", err);
  }


  // Sammle alle konvertierten Dokumente
  const updatedDocuments = [...newEntry.documents];
  const convertedDocumentIds = [];

  // Process each file that needs conversion
  for (const document of files) {
    const file = document.file;

    const inputPath = path.join(dir, file.url);
    const outputFileName = file.name.replace(/\.[^/.]+$/, `_converted${ext}`);
    const outputPath = path.join(convertedDir, outputFileName);

    try {
      // Convert the file
      const pdfBuf = await libreOfficeFileConverter.convert({
        format: ext,
        input: "file",
        inputPath,
        output: "file",
        outputPath
      });

      // Upload the converted file to Strapi
      debugger;
      const uploadedFiles = await uploadFile(outputPath);
      debugger;

      // Get the converted file ID
      const convertedFile = uploadedFiles[0];
      const convertedFileId = convertedFile.id;

      // Update the document in our local array
      const docIndex = updatedDocuments.findIndex(doc => doc.id === document.id);
      if (docIndex !== -1) {
        updatedDocuments[docIndex] = {
          ...updatedDocuments[docIndex],
          id: document.id,
          name: document.name,
          // @ts-ignore
          file: document.file?.id,
          converted_file: convertedFileId
        };

        // Speichere die ID des konvertierten Dokuments
        convertedDocumentIds.push(document.id);
      }

    } catch (err) {
      debugger;
      console.log("Error converting file", err);
      strapi.log.alert(err);
      strapi.log.error(err);
    }
  }

  // Aktualisiere jedes konvertierte Dokument einzeln
  for (const documentId of convertedDocumentIds) {
    try {
      // Finde das Dokument in unserem Array
      const docToUpdate = updatedDocuments.find(doc => doc.id === documentId);
      if (!docToUpdate) continue;

      // Hole das aktuelle Event, um die aktuellen Dokumente zu bekommen
      // @ts-ignore - Wir ignorieren TypeScript-Fehler hier, da wir wissen, dass documents existiert
      const currentEvent = await strapi.entityService.findOne(
        event.model.uid as ContentType,
        id,
        {
          populate: {
            // @ts-ignore - documents ist ein gültiges Feld im Event-Modell
            documents: {
              populate: {
                file: true,
                converted_file: true
              }
            }
          }
        }
      );

      // Finde das Dokument im aktuellen Event
      // @ts-ignore - Wir wissen, dass currentEvent.documents existiert
      const currentDocuments = toArray(currentEvent.documents);
      const currentDocIndex = currentDocuments.findIndex(doc => doc.id === documentId);

      if (currentDocIndex === -1) {
        console.log(`Document with ID ${documentId} not found in current event`);
        continue;
      }

      // Bereite das Update vor
      // @ts-ignore
      const convertedFileId = typeof docToUpdate.converted_file === 'object' && docToUpdate.converted_file?.id
        ? docToUpdate.converted_file.id
        : docToUpdate.converted_file;

      console.log(`Updating document ${documentId} with converted_file ${convertedFileId}`);

      // Wir versuchen nicht mehr, die Komponente direkt zu aktualisieren,
      // da dies in Strapi 5 nicht mehr unterstützt wird
      console.log(`Skipping direct component update, using event update instead`);

      // Alternativ: Aktualisiere das gesamte Event mit den aktualisierten Dokumenten
      const updatedEventDocuments = currentDocuments.map(doc => {
        if (doc.id === documentId) {
          return {
            ...doc,
            converted_file: convertedFileId
          };
        }
        return doc;
      });

      // @ts-ignore
      const result = await strapi.entityService.update(
        event.model.uid as ContentType,
        id,
        {
          data: {
            // @ts-ignore
            documents: updatedEventDocuments.map(doc => ({
              id: doc.id,
              name: doc.name,
              file: doc.file?.id,
              converted_file: doc.id === documentId ? convertedFileId : (doc.converted_file?.id || doc.converted_file)
            }))
          }
        }
      );

      console.log(`Updated document ${documentId} with converted PDF`);
    } catch (err) {
      debugger;
      console.log("Error updating event with converted files", err);
      strapi.log.alert(err);
      strapi.log.error(err);
    }
  }




}

const uploadFile = async (filePath: string) => {
  try {
    // Datei einlesen oder Stream erstellen
    const fileStream = createReadStream(filePath);
    const fileName = path.basename(filePath);
    debugger;

    // Strapi Upload Service korrekt aufrufen
    const result = await strapi
      .plugin('upload')
      .service('upload')
      .upload({
        files: {
          filepath: filePath,
          originalFileName: fileName,
          mimetype: 'application/pdf', // oder der entsprechende MIME-Typ
          size: (await promises.stat(filePath)).size,
          //stream: fileStream,
        },
        data: {
          fileInfo: {
            alternativeText: `Converted file ${fileName}`,
            caption: fileName,
            name: fileName,
          },
        },
      });

    console.log("File upload result:", result);
    return result;
  } catch (error) {
    debugger;
    strapi.log.error(`Upload error for ${filePath}: ${error.message}`);
    throw error;
  }
};



export default {

  afterCreate: async (event: StrapiEvent) => {
    await convertUploadedFiles(event);
    await sendEmails(event);
  },
  afterUpdate: async (event: StrapiEvent) => {
    await convertUploadedFiles(event);
    await sendEmails(event);
  }
}

