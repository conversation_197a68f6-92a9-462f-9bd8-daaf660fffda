{"kind": "collectionType", "collectionName": "messages", "info": {"singularName": "message", "pluralName": "messages", "displayName": "Message", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"text": {"type": "text", "required": true}, "anonymous_email": {"type": "email"}, "type": {"type": "enumeration", "enum": ["<PERSON><PERSON>", "Accept", "Comment"], "default": "Comment", "required": true}, "events": {"type": "relation", "relation": "manyToMany", "target": "api::event.event", "mappedBy": "messages"}}}