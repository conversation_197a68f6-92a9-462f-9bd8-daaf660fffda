// import type { Core } from '@strapi/strapi';

import { exec } from "node:child_process";

export default {
  /**
   * An asynchronous register function that runs before
   * your application is initialized.
   *
   * This gives you an opportunity to extend code.
   */
  register(/* { strapi }: { strapi: Core.Strapi } */) {},

  /**
   * An asynchronous bootstrap function that runs before
   * your application gets started.
   *
   * This gives you an opportunity to set up your data model,
   * run jobs, or perform some special logic.
   */
  bootstrap(/* { strapi }: { strapi: Core.Strapi } */) {
    // Cleanup logic here. run the sync script to copy the types into the frontend folder
    console.log("run synchronization script");
    exec("yarn sync:run", (error, stdout, stderr) => {
      if (error) {
        console.error(`[destroy] ${error.message}`);
        return;
      }
      if (stderr) console.warn(`[destroy] ${stderr}`);
      console.log(`[destroy] ${stdout.trim()}`);
    });
  },
};
