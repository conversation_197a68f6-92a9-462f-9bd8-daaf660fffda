export default [
  //'strapi::logger',
  //'strapi::errors',
  {
    name: 'strapi::logger',
    config: {
      level: 'debug', // Erhöht das Log-Level
    }
  },
  {
    name: 'strapi::errors',
    config: {
      // Fügt detailliertere Fehlerinformationen hinzu
      showErrorDetails: true,
    }
  },
 // 'strapi::security',
  //'strapi::cors',
  {
    name: 'strapi::cors',
    config: {
      level: "debug",
      enabled: true,
      headers: '*',
      origin: ['http://localhost:3000', 'http://localhost:1337', '*'], // Fügen Sie hier Ihre Frontend-Domain hinzu
    },
  },
  'strapi::poweredBy',
  'strapi::query',
  'strapi::body',
  'strapi::session',
  'strapi::favicon',
  'strapi::public',

  // https://market.strapi.io/plugins/strapi-plugin-email-designer-5
  {
    name: "strapi::security",
    config: {
      contentSecurityPolicy: {
        useDefaults: true,
        directives: {
          "script-src": ["'self'", "'unsafe-inline'", "editor.unlayer.com"],
          "frame-src": ["'self'", "editor.unlayer.com"],
          upgradeInsecureRequests: null,
        },
      },
    },
  }
];
