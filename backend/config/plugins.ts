import type { EmailConfig } from "strapi-plugin-email-designer-5/dist/server/src";


export default ({ env }) => ({
  "gen-types": {
    enabled: true,
    config: {
      // do not change! types are copied from backend to frontend on strapi starup
      outputLocation: "types/auto_generated",
      // If this is true, then the outputLocation should be the location to a .ts file
      singleFile: false,
    },
  },
  "email-designer-5": {
    enabled: true,
    // Your custom configuration here
    config: {
      // Here the Merge Tags defined will be merged with the defaults above
      mergeTags: {
        company: {
          name: "Company",
          value: "{{company}}",
          sample: "Esploro One",
        },
      },
      features: {
        smartMergeTags: true,
        //blocks: false,
        undoRedo: true,
        sendTestEmail: true,
        preview: true,
        textEditor: {
          spellChecker: true,
          cleanPaste: 'confirm',
        },
        pageAnchors: true,
      },
      tools: {
        timer: {
          enable: true
        },
        video: {
          enable: true,
        }
      }
    } as EmailConfig,
  },

});