{"name": "esploro-one-backend", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "seed:example": "node ./scripts/seed.js", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry", "types": "strapi ts:generate-types --debug", "sync:run": "rsync -av --delete ./types/auto_generated ../frontend/src/types/"}, "dependencies": {"libreoffice-file-converter": "3.2.0", "node-fetch": "3.3.2", "strapi-plugin-media-upload": "1.1.2", "@offset-dev/strapi-calendar": "1.0.0", "@strapi/plugin-cloud": "5.12.7", "@strapi/plugin-documentation": "5.12.7", "@strapi/plugin-users-permissions": "5.12.7", "@strapi/strapi": "5.12.7", "better-sqlite3": "11.3.0", "fs-extra": "^10.0.0", "lodash": "4.17.21", "mime-types": "^2.1.27", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "strapi-plugin-email-designer-5": "0.0.7", "strapi-plugin-gen-types": "0.0.15", "styled-components": "6.1.17"}, "devDependencies": {"@types/lodash": "4.17.16", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "ts-node": "10.9.2", "typescript": "^5", "@types/fs-extra": "11.0.4"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************"}}