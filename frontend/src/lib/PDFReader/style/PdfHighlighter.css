.PdfHighlighter {
  position: absolute;
  overflow: auto;
  width: 100%;
  height: 100%;
  background-color: var(--color-white-dark);
}

/* Style the scrollbar */
.PdfHighlighter::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.PdfHighlighter::-webkit-scrollbar-thumb {
  background-color: var(--color-grey);
  border-radius: 5px;
}

.PdfHighlighter::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-grey-hover);
}

.PdfHighlighter::-webkit-scrollbar-track {
  background-color: var(--color-white-dark);
  border-radius: 5px;
}

.PdfHighlighter::-webkit-scrollbar-track-piece {
  background-color: var(--color-white-dark);
}

.PdfHighlighter__tip-container {
  z-index: 6;
  position: absolute;
}

.PdfHighlighter--disable-selection {
  user-select: none;
  pointer-events: none;
}
