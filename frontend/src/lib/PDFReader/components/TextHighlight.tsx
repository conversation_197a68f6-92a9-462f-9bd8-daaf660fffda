import React, { CSSProperties, MouseEvent } from "react";

import "../style/TextHighlight.css";

import type { ViewportHighlight } from "../types";
import { usePdfHighlighterContext } from "../contexts/PdfHighlighterContext";
import { usePDFContext } from "@/components/Document/PDFContext";

/**
 * The props type for {@link TextHighlight}.
 *
 * @category Component Properties
 */
export interface TextHighlightProps {
  /**
   * Highlight to render over text.
   */
  highlight: ViewportHighlight;

  /**
   * Callback triggered whenever the user clicks on the part of a highlight.
   *
   * @param event - Mouse event associated with click.
   */
  onClick?(event: MouseEvent<HTMLDivElement>): void;

  /**
   * Callback triggered whenever the user enters the area of a text highlight.
   *
   * @param event - Mouse event associated with movement.
   */
  onMouseOver?(event: MouseEvent<HTMLDivElement>): void;

  /**
   * Callback triggered whenever the user leaves  the area of a text highlight.
   *
   * @param event - Mouse event associated with movement.
   */
  onMouseOut?(event: MouseEvent<HTMLDivElement>): void;

  /**
   * Indicates whether the component is autoscrolled into view, affecting
   * default theming.
   */
  isScrolledTo: boolean;

  /**
   * Callback triggered whenever the user tries to open context menu on highlight.
   *
   * @param event - Mouse event associated with click.
   */
  onContextMenu?(event: MouseEvent<HTMLDivElement>): void;

  /**
   * Optional CSS styling applied to each TextHighlight part.
   */
  style?: CSSProperties;

  //context: PDFContextType;
}

/**
 * A component for displaying a highlighted text area.
 *
 * @category Component
 */
export const TextHighlight = ({
  highlight,
  onClick,
  onMouseOver,
  onMouseOut,
  isScrolledTo,
  onContextMenu,
  style,
  //context
}: TextHighlightProps) => {
  const highlightClass = isScrolledTo ? "TextHighlight--scrolledTo" : "";
  const { rects } = highlight.position;

  const { isSelectionInProgress } = usePdfHighlighterContext();
  const { activeHighlightId, hoveredHighlightId } = usePDFContext();

  const isActive = activeHighlightId === highlight.id;
  const isHovered = hoveredHighlightId === highlight.id;

  const className = isActive ? 'bg-warning dark:bg-warning-dark' : isHovered ? 'bg-warning/10 dark:bg-warning-dark' : 'bg-warning/10 opacity-45';

  return (
    <div
      className={`TextHighlight ${highlightClass} ${isHovered || isActive ? '' : 'mix-blend-multiply'} `}
      onContextMenu={onContextMenu}
    >
      <div className="TextHighlight__parts">
        {rects.map((rect, index) => (
          <div
            onMouseOver={onMouseOver}
            onMouseOut={onMouseOut}
            onClick={onClick}
            key={index}
            style={{
              ...rect,
              ...style,
              pointerEvents: isSelectionInProgress ? "none" : "auto",
              //opacity: isSelectionInProgress ? "0.3" : "1",
            }}
            className={`TextHighlight__part  cursor-pointer absolute ${className}`}
          />
        ))}
      </div>
    </div>
  );
};
