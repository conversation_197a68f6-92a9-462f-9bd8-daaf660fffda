import { filter, uniqueId } from "lodash";

export const htmlIdRegEx = /[^a-z0-9\-_:\.]|^[^a-z]+/gi;
export const startsWithNumberRegEx = /^\d/g;

export const matchRegex = (expression: string, regEx: RegExp): string[] => {
  // regExt.exec() does not do the same thing!
  // eslint-disable-next-line @typescript-eslint/prefer-regexp-exec
  const array = expression?.match(regEx) || [];

  return filter(array);
};


const isFirstCharNum = (str: string): boolean => !!matchRegex(str, startsWithNumberRegEx).length;

export const sanitizeId = (id: string | number| undefined): string => {
  if (!id) {
    id = uniqueId();
  }
  id = `${id}`;
  if (isFirstCharNum(id)) {
    id = `a${id}`;
  }
  return id.replaceAll(htmlIdRegEx, '_');
};