import { strapi } from "@strapi/client";
import { BaseQueryParams } from "@strapi/client/dist/types/content-api";
import { FindMany } from "@/types/auto_generated/findManyPayload";
import {
  CollectionTypeMap,
  ECollectionTypes,
  ModelFields,
  PopulateFields,
  PopulateRequestObject
} from "@/utility/strapi/types";
import { FindOne } from "@/types/auto_generated/findOnePayload";

export const getStrapiClient = () => strapi({
  baseURL: `${process.env.NEXT_PUBLIC_STRAPI_URL}/api`,
  auth: process.env.NEXT_PUBLIC_STRAPI_TOKEN
});

export const getCollection = (name: ECollectionTypes) => getStrapiClient().collection(name);

export const findMany = <
  // T is the base model
  T extends keyof CollectionTypeMap,
  // F are the fields, which you want only
  F extends ModelFields<CollectionTypeMap[T]>,
  // P populate for subobjects inside this
  P extends PopulateFields<CollectionTypeMap[T]>[] | PopulateRequestObject<CollectionTypeMap[T]>
>(
  name: T,
  queryParams?: Omit<BaseQueryParams, "fields" | "populate"> & {
    fields?: F[];
    populate?: P;
  }
) => {
  type BaseType = CollectionTypeMap[T];
  type ResultType = Pick<BaseType, F | (F extends never ? keyof BaseType : never)> &
    (P extends string[]
      ? { [K in P[number] & keyof BaseType]-?: NonNullable<BaseType[K]> }
      : P extends PopulateRequestObject<BaseType>
        ? {
          [K in keyof P]-?: P[K] extends { fields: Array<infer Fields> }
            ? NonNullable<BaseType[K & keyof BaseType]> extends (infer U)[]
              ? Array<Pick<NonNullable<U>, Fields & keyof U>>
              : Pick<NonNullable<BaseType[K & keyof BaseType]>, Fields & keyof BaseType[K & keyof BaseType]>
            : NonNullable<BaseType[K & keyof BaseType]>
        }
        : {});

  return getCollection(name).find(queryParams as BaseQueryParams) as unknown as Promise<FindMany<ResultType>>;
};

export const findOne = <
  T extends keyof CollectionTypeMap,
  F extends ModelFields<CollectionTypeMap[T]>,
  P extends PopulateFields<CollectionTypeMap[T]>[] | PopulateRequestObject<CollectionTypeMap[T]>
>(
  name: T,
  documentId: string,
  queryParams?: Omit<BaseQueryParams, "fields" | "populate"> & {
    fields?: F[];
    populate?: P;
  }
) => {
  type BaseType = CollectionTypeMap[T];
  type ResultType = Pick<BaseType, F | (F extends never ? keyof BaseType : never)> &
    (P extends string[]
      ? { [K in P[number] & keyof BaseType]-?: NonNullable<BaseType[K]> }
      : P extends PopulateRequestObject<BaseType>
        ? {
          [K in keyof P]-?: P[K] extends { fields: Array<infer Fields> }
            ? NonNullable<BaseType[K & keyof BaseType]> extends (infer U)[]
              ? Array<Pick<NonNullable<U>, Fields & keyof U>>
              : Pick<NonNullable<BaseType[K & keyof BaseType]>, Fields & keyof BaseType[K & keyof BaseType]>
            : NonNullable<BaseType[K & keyof BaseType]>
        }
        : {});

  return getCollection(name).findOne(documentId, queryParams as BaseQueryParams) as unknown as Promise<FindOne<ResultType>>;
};