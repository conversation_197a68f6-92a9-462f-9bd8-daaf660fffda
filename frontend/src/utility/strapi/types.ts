import { WorkingPackage } from "@/types/auto_generated/workingPackage";
import { Event } from "@/types/auto_generated/event";
import { Status } from "@/types/auto_generated/status";
import { Message } from "@/types/auto_generated/message";
import { SharedMedia } from "@/types/auto_generated/sharedMedia";

export enum ECollectionTypes {
  working_package = "working-packages",
  event = "events",
  status = "statuses",
  message = "messages",
  sharedMedia = "shared-media",
}

export type CollectionTypeMap = {
  [ECollectionTypes.working_package]: WorkingPackage;
  [ECollectionTypes.event]: Event;
  [ECollectionTypes.status]: Status;
  [ECollectionTypes.message]: Message;
  [ECollectionTypes.sharedMedia]: SharedMedia;
}

// "Basically" for findMany you can have "fields" and "populate"
// the following code allows "fields" to be simple model fields like strings
// all arrays only come from "populate" in the api
// @see BaseQueryParams
// populate can be string[] or nested object with additional fields (and perhaps even more populate?)

type Primitive = string | number | boolean | Date;
// Hilfsfunktion für Array-Typen
type UnwrapArray<T> = T extends (infer U)[] ? U : T;

// Hilfsfunktion für Null/Undefined
type Unwrap<T> = T extends null | undefined ? never : T;

// Für direkte Felder (Primitive)
export type ModelFields<T> = {
  [K in keyof T]: UnwrapArray<Unwrap<T[K]>> extends Primitive ? K : never;
}[keyof T];

// für populate fields (unterobjekte oder arrays)
export type PopulateFields<T> = {
  [K in keyof T]: UnwrapArray<Unwrap<T[K]>> extends Primitive ? never : K;
}[keyof T];

// ??? code
export type ExtractRelationType<T> = T extends (infer U)[]
  ? ExtractRelationType<U>
  : T extends null | undefined
    ? never
    : T extends { id?: number }
      ? T
      : never;

export type PopulateRequestObject<T> = {
  [K in PopulateFields<T>]?: boolean | {
  fields?: ModelFields<ExtractRelationType<T[K]>>[];
  populate?:  PopulateFields<ExtractRelationType<T[K]>>[] | PopulateRequestObject<ExtractRelationType<T[K]>>;
};
};