import { toArray } from "lodash";
import { findMany } from "@/utility/strapi/StrapiUtility";
import { ECollectionTypes } from "@/utility/strapi/types";
import WorkingPackageListItem from "@/components/WorkingPackage/WorkingPackageListItem";

export default async function Home() {

  const myPackages = await findMany(ECollectionTypes.working_package, {
    //fields: ["name"],
    // v1
    // populate: ["users_permissions_users"],
    // v2
    // populate: {
    //   users_permissions_users: true
    // }
    // v3
    /*
    populate: {
      users_permissions_users: {
        fields: ["email"],
        populate: {
          role: {
            fields: ["type"],
          }
        }
      }
    }

     */

    populate: {
      users_permissions_users: {
        fields: ["email"],
      }
    }
  });


  console.log({
    myPackages,
  });

  return (
    <div className="container mx-auto">
      <h1 className="mb-8 text-3xl font-bold">Welcome to Esploro One</h1>

      <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-white-dark">
        <h2 className="mb-4 text-xl font-semibold">Working Packages</h2>
        <ol className="flex flex-col gap-4 font-mono">
          {toArray(myPackages.data).map((item, index) => <WorkingPackageListItem key={index} {...item}/>)}
        </ol>
      </div>
    </div>
  );
}
