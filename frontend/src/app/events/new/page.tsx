import { EventFormWrapper } from "@/components/Event/Form/EventFormWrapper";
import { SelectOption } from "@/components/Form/components/FormSelectInput";
import { findMany } from "@/utility/strapi/StrapiUtility";
import { ECollectionTypes } from "@/utility/strapi/types";
import { WorkingPackage } from "@/types/auto_generated/workingPackage";
import Link from "next/link";

export default async function NewEventPage() {
  // Load working packages for select options
  const workingPackages = await findMany(ECollectionTypes.working_package, {
    fields: ['name', 'documentId']
  });

  const workingPackageOptions: SelectOption[] = workingPackages.data?.map((wp: WorkingPackage) => ({
    value: wp.documentId || '',
    label: wp.name || ''
  })) || [];

  return (
    <div>
      <div className="mb-4">
        <Link
          href="/events"
          className="inline-flex items-center gap-2 text-color-accent hover:text-color-accent-hover dark:text-color-accent-dark dark:hover:text-color-accent-hover transition-colors"
        >
          ← Zurück zu Events
        </Link>
      </div>
      <EventFormWrapper workingPackageOptions={workingPackageOptions} />
    </div>
  );
}
