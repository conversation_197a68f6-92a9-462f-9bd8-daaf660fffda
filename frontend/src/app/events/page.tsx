import { toArray } from "lodash";
import { findMany } from "@/utility/strapi/StrapiUtility";
import { ECollectionTypes } from "@/utility/strapi/types";
import Link from "next/link";

export default async function EventsPage() {
  const events = await findMany(ECollectionTypes.event, {
    populate: {
      messages: {
        fields: ["documentId"],
      },
      documents: {
        fields: ["id"],
      }
    }
  });

  return (
    <div className="container mx-auto">
      <div className="mb-8 flex items-center justify-between">
        <h1 className="text-3xl font-bold">Events</h1>
        <Link
          href="/events/new"
          className="inline-flex items-center gap-2 rounded-lg px-4 py-2 text-white"
        >
          + Neues Event erstellen
        </Link>
      </div>

      <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-white-dark">
        <ul className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
          {toArray(events.data).map((event, index) => (
            <li key={index} className="rounded-lg border border-grey p-4 transition-apple hover:shadow-md dark:border-primary">
              <Link href={`/events/${event.documentId}`} className="flex flex-col gap-2">
                <h3 className="text-lg font-semibold text-white">{event.name}</h3>
                <div className="flex gap-4">
                  <p className="text-sm text-grey dark:text-grey-dark">
                    {event.messages?.length || 0} Messages
                  </p>
                  <p className="text-sm text-grey dark:text-grey-dark">
                    {event.documents?.length || 0} Documents
                  </p>
                </div>
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}
