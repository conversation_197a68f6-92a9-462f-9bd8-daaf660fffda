'use server';

import { getCollection } from "@/utility/strapi/StrapiUtility";
import { ECollectionTypes } from "@/utility/strapi/types";
import { revalidatePath } from "next/cache";
import { Message } from "@/types/auto_generated/message";

type MessageFormData = {
  text: string;
  type: 'Comment' | 'Accept' | 'Deny';
  anonymous_email?: string;
};

/**
 * Create a new message for an event
 */
export async function createMessage(eventId: string, formData: MessageFormData) {
  try {
    // Create the message
    const messageCollection = getCollection(ECollectionTypes.message);
    console.log("Creating message with data:", formData);

    // Create the message with the form data and associate it with the event
    // Using Strapi 5 API format with the connect syntax for relations
    const messageResponse = await messageCollection.create({
      text: formData.text,
      type: formData.type,
      anonymous_email: formData.anonymous_email,
      events: {
        connect: [eventId]
      }
    });

    if (!messageResponse || !messageResponse.data) {
      throw new Error('Failed to create message');
    }

    // Revalidate the page to show the new message
    revalidatePath(`/events/${eventId}`);

    return messageResponse;
  } catch (error) {
    console.error('Error creating message:', error);
    throw error;
  }
}
