import { ECollectionTypes } from "@/utility/strapi/types";
import { findOne } from "@/utility/strapi/StrapiUtility";

// Components
import EventInfo from "@/components/Event/EventInfo";
import DocumentList from "@/components/Document/DocumentList";

// this page displays the details of a single event
// there is a part with general event information (db fields etc)
// there is also the list of messages styled like a chat
// then at last there is a button to create a new message which sends a request to create a new message
export default async function Page({
  params,
}: {
  params: Promise<{ slug: string }>
}) {
  const { slug } = await params;

  const details = await findOne(ECollectionTypes.event, slug, {
    populate: {
      working_packages: {
        fields: ["name", "documentId"],
        populate: {
          users_permissions_users: {
            fields: ["email"]
          }
        }
      },
      messages: {
        fields: ["text", "type", "anonymous_email", "createdAt"],
      },
      documents: {
        fields: ["name", "id"],
        populate: {
          file: {
            fields: ["url", "mime", "name"]
          },
          messages: {
            fields: ["id"]
          }
        }
      }
    }
  });


  console.log({
    details
  })


  if (!details || !details.data) {
    return (
      <div className="container mx-auto p-8">
        <div className="rounded-lg bg-red-50 p-6 text-red-800 dark:bg-red-900/20 dark:text-red-300">
          <h1 className="text-xl font-bold">Event Not Found</h1>
          <p className="mt-2">The event you are looking for does not exist or has been removed.</p>
        </div>
      </div>
    );
  }

  const event = details.data;

  return (
    <div className="container mx-auto p-4">
      <div className="flex flex-col gap-8">
        {/* Event Information Section */}
        <EventInfo event={event} />

        {/* Documents Section */}
        <section className="rounded-lg bg-white p-6 shadow-sm dark:bg-white-dark">
          <div className="mb-6 flex items-center justify-between">
            <h2 className="text-xl font-semibold">Documents</h2>
          </div>
          <DocumentList event={event} />
        </section>
      </div>
    </div>
  );
}