import { DocumentFormWrapper } from "@/components/Document/Form/DocumentFormWrapper";
import Link from "next/link";

interface NewDocumentPageProps {
  params: Promise<{ slug: string }>;
}

export default async function NewDocumentPage({ params }: NewDocumentPageProps) {
  const { slug } = await params;

  return (
    <div>
      <div className="mb-4">
        <Link
          href={`/events/${slug}`}
          className="inline-flex items-center gap-2 text-color-accent hover:text-color-accent-hover dark:text-color-accent-dark dark:hover:text-color-accent-hover transition-colors"
        >
          ← Zurück zum Event
        </Link>
      </div>
      <DocumentFormWrapper eventId={slug} />
    </div>
  );
}
