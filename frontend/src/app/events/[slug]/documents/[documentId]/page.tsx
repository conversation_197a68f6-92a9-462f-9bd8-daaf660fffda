import { ECollectionTypes } from "@/utility/strapi/types";
import { findOne } from "@/utility/strapi/StrapiUtility";
import Link from "next/link";
import { toArray } from "lodash";
import PDFViewerWrapper from "@/components/Document/PDFViewerWrapper";

export default async function DocumentDetailPage({
  params,
}: {
  params: Promise<{ slug: string; documentId: string }>;
}) {
  const { slug, documentId } = await params;

  // Fetch the event details
  const eventDetails = await findOne(ECollectionTypes.event, slug, {
    populate: {
      documents: {
        fields: ["name", "id"],
        populate: {
          file: {
            fields: ["url", "mime", "name"]
          },
          converted_file: {
            fields: ["url", "mime", "name"]
          },
          messages: {
            fields: ["id", "text", "type", "anonymous_email", "createdAt"],
          },
        }
      }
    }
  });

  if (!eventDetails || !eventDetails.data) {
    return (
      <div className="container mx-auto p-8">
        <div className="rounded-lg bg-highlight/10 p-6 text-highlight dark:bg-highlight-dark/20 dark:text-highlight-hover">
          <h1 className="text-xl font-bold">Event nicht gefunden</h1>
          <p className="mt-2">Das gesuchte Event existiert nicht oder wurde entfernt.</p>
        </div>
      </div>
    );
  }

  const event = eventDetails.data;

  // Find the specific document
  const document = toArray(event.documents).find(doc => doc.id?.toString() === documentId);

  if (!document) {
    return (
      <div className="container mx-auto p-8">
        <div className="rounded-lg bg-highlight/10 p-6 text-highlight dark:bg-highlight-dark/20 dark:text-highlight-hover">
          <h1 className="text-xl font-bold">Dokument nicht gefunden</h1>
          <p className="mt-2">Das gesuchte Dokument existiert nicht oder wurde entfernt.</p>
          <Link
            href={`/events/${event.documentId}`}
            className="mt-4 inline-block rounded-md bg-accent px-4 py-2 text-white hover:bg-accent-hover dark:bg-accent-dark dark:hover:bg-accent-hover"
          >
            Zurück zum Event
          </Link>
        </div>
      </div>
    );
  }

  console.log({
    event,
    document,
  })

  return (
    <div className="container mx-auto p-4">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-2xl font-bold">{document.name || 'Document'}</h1>
        <Link
          href={`/events/${slug}`}
          className="rounded-md bg-accent px-4 py-2 text-white hover:bg-accent-hover dark:bg-accent-dark dark:hover:bg-accent-hover"
        >
          Zurück zum Event
        </Link>
      </div>

      <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-white-dark h-full">
        <PDFViewerWrapper
          fileUrl={document.converted_file?.url || document.file?.url}
          />
      </div>
    </div>
  );
}
