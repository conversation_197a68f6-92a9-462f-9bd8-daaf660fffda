'use server';

import { getCollection } from "@/utility/strapi/StrapiUtility";
import { ECollectionTypes } from "@/utility/strapi/types";
import { revalidatePath } from "next/cache";
import { DocumentCommentFormData } from "@/components/Document/DocumentCommentForm";

/**
 * Create a new comment for a document
 */
export async function createDocumentComment(
  eventId: string, 
  documentId: string, 
  formData: DocumentCommentFormData
) {
  try {
    // Create the message
    const messageCollection = getCollection(ECollectionTypes.message);
    console.log("Creating document comment with data:", formData);

    // In a real implementation, we would store the position data in a structured way
    // For this prototype, we'll include it in the text
    let messageText = formData.text;
    
    if (formData.position) {
      messageText += `\n\nPosition: Page ${formData.position.pageNumber}, X: ${formData.position.x.toFixed(1)}%, Y: ${formData.position.y.toFixed(1)}%`;
    }

    // Create the message with the form data and associate it with the event
    const messageResponse = await messageCollection.create({
      text: messageText,
      type: 'Comment',
      anonymous_email: formData.anonymous_email,
      events: {
        connect: [eventId]
      }
      // In a real implementation, we would also connect this to the specific document
      // This would require extending the Message schema to include a relation to documents
    });

    if (!messageResponse || !messageResponse.data) {
      throw new Error('Failed to create document comment');
    }

    // Revalidate the event page to show the new comment
    revalidatePath(`/events/${eventId}/documents/${documentId}`);
    revalidatePath(`/events/${eventId}`);

    return messageResponse.data;
  } catch (error) {
    console.error('Error creating document comment:', error);
    throw error;
  }
}
