import { EventFormWrapper } from "@/components/Event/Form/EventFormWrapper";
import { SelectOption } from "@/components/Form/components/FormSelectInput";
import { findMany, findOne } from "@/utility/strapi/StrapiUtility";
import { ECollectionTypes } from "@/utility/strapi/types";
import { Event } from "@/types/auto_generated/event";
import { WorkingPackage } from "@/types/auto_generated/workingPackage";
import Link from "next/link";

interface EditEventPageProps {
  params: Promise<{ slug: string }>;
}

export default async function EditEventPage({ params }: EditEventPageProps) {
  const { slug } = await params;

  // Load event data and working packages in parallel
  const [eventData, workingPackages] = await Promise.all([
    findOne(ECollectionTypes.event, slug, {
      populate: {
        working_packages: {
          fields: ['documentId']
        }
      }
    }),
    findMany(ECollectionTypes.working_package, {
      fields: ['name', 'documentId']
    })
  ]);

  const event = eventData.data as Event;
  const workingPackageOptions: SelectOption[] = workingPackages.data?.map((wp: WorkingPackage) => ({
    value: wp.documentId || '',
    label: wp.name || ''
  })) || [];

  if (!event) {
    return (
      <div className="container mx-auto p-8">
        <div className="rounded-lg bg-color-highlight/10 border border-color-highlight p-6 dark:bg-color-highlight/20">
          <h1 className="text-xl font-bold text-color-highlight">Event nicht gefunden</h1>
          <p className="mt-2 text-color-highlight">Das gesuchte Event existiert nicht oder wurde entfernt.</p>
          <Link
            href="/events"
            className="mt-4 inline-block rounded-md bg-color-accent px-4 py-2 text-color-white hover:bg-color-accent-hover dark:bg-color-accent-dark dark:hover:bg-color-accent-hover"
          >
            Zurück zu Events
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-4">
        <Link
          href={`/events/${slug}`}
          className="inline-flex items-center gap-2 text-color-accent hover:text-color-accent-hover dark:text-color-accent-dark dark:hover:text-color-accent-hover transition-colors"
        >
          ← Zurück zum Event
        </Link>
      </div>
      <EventFormWrapper event={event} workingPackageOptions={workingPackageOptions} />
    </div>
  );
}
