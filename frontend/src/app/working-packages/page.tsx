import { toArray } from "lodash";
import { findMany } from "@/utility/strapi/StrapiUtility";
import { ECollectionTypes } from "@/utility/strapi/types";
import WorkingPackageListItem from "@/components/WorkingPackage/WorkingPackageListItem";

export default async function WorkingPackagesPage() {
  const myPackages = await findMany(ECollectionTypes.working_package, {
    populate: {
      users_permissions_users: {
        fields: ["email"],
      }
    }
  });

  return (
    <div className="container mx-auto">
      <h1 className="mb-8 text-3xl font-bold">Working Packages</h1>

      <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-white-dark">
        <ol className="flex flex-col gap-4 font-mono">
          {toArray(myPackages.data).map((item, index) => <WorkingPackageListItem key={index} {...item}/>)}
        </ol>
      </div>
    </div>
  );
}
