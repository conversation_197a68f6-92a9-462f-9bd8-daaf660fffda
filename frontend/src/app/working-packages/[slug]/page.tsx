import { findOne } from "@/utility/strapi/StrapiUtility";
import { ECollectionTypes } from "@/utility/strapi/types";
import Link from "next/link";

export default async function Page({
  params,
}: {
  params: Promise<{ slug: string }>
}) {
  const { slug } = await params

  const postDetails = await findOne(ECollectionTypes.working_package, slug, {
    populate: {
      users_permissions_users: true,
      events: {
        populate: {
          statuses: {
            fields: ["id"],
          }
        }
      }
    }
  });
  if (!postDetails || !postDetails.data) {
    return <div>not found</div>
  }

  return (
    <div className="container mx-auto">
      <div className="flex">
        <Link href="/" className="ml-auto rounded-lg bg-accent px-4 py-2 text-white hover:bg-accent-hover dark:bg-accent dark:hover:bg-accent-hover">
          Back
        </Link>
      </div>

      <h1 className="mt-6 text-2xl font-bold">{postDetails.data.name}</h1>

      <section className="mt-8">
        <h2 className="mb-4 text-xl font-semibold" id="events">Events</h2>
        <ul className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3" role="list" aria-labelledby="events">
          {postDetails.data.events.map((event, index) => (
            <li key={index} className="rounded-lg border border-grey p-3 dark:border-primary">
              <Link href={`/events/${event.documentId}`} className="font-medium hover:text-accent dark:hover:text-accent-hover">
                {event.name}
              </Link>
              <p className="mt-1 text-sm text-grey dark:text-grey-dark">
                {event.statuses?.length || 0} Statuses
              </p>
            </li>
          ))}
        </ul>
      </section>

      <section className="mt-8">
        <h2 className="mb-4 text-xl font-semibold" id="users">Relevante Benutzer</h2>
        <ul className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3" role="list" aria-labelledby="users">
          {postDetails.data.users_permissions_users.map((user, index) => (
            <li key={index} className="rounded-lg border border-grey p-3 dark:border-primary">
              <span className="font-medium">{user.email}</span>
            </li>
          ))}
        </ul>
      </section>
    </div>
  )
}
