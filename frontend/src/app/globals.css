@import 'tailwindcss';


@theme {


  /* Light Theme Colors */
  --color-primary: orange;
  --color-primary-hover: orange;
  --color-accent: blue;
  --color-accent-hover: lightblue;
  --color-highlight: red;
  --color-highlight-hover: lightcoral;
  --color-warning: yellow;
  --color-warning-hover: lightyellow;
  --color-grey: grey;
  --color-grey-hover: lightgrey;
  --color-white: white;
  --color-white-hover: lightgrey;
  --color-background: #f0f0f0;

  /* Dark Theme Colors */
  --color-primary-dark: darkorange;
  --color-primary-hover-dark: orange;
  --color-accent-dark: darkblue;
  --color-accent-hover-dark: blue;
  --color-highlight-dark: darkred;
  --color-highlight-hover-dark: red;
  --color-warning-dark: gold;
  --color-warning-hover-dark: yellow;
  --color-grey-dark: darkgrey;
  --color-grey-hover-dark: grey;
  --color-white-dark: #1a1a1a;
  --color-white-hover-dark: #2a2a2a;
  --color-background-dark: #494949;
}

 /*dark mode with toggle*/
@custom-variant dark (&:where(.dark, .dark *));

@keyframes border-dance {
  0% {
    background-position: 0 0, 100% 100%, 0 100%, 100% 0;
  }
  100% {
    background-position: 100% 0, 0 100%, 0 0, 100% 100%;
  }
}

.rotating-border {
  background: linear-gradient(90deg, var(--color-primary) 50%, transparent 50%), linear-gradient(90deg, var(--color-primary) 50%, transparent 50%), linear-gradient(0deg, var(--color-primary) 50%, transparent 50%), linear-gradient(0deg, var(--color-primary) 50%, transparent 50%);
  background-repeat: repeat-x, repeat-x, repeat-y, repeat-y;
  background-size: 12px 3px, 12px 3px, 3px 12px, 3px 12px;
  animation: border-dance 24s infinite linear;
}
.static-border {
  border: 3px dashed var(--color-primary);
}