import { toArray } from "lodash";
import { findMany } from "@/utility/strapi/StrapiUtility";
import { ECollectionTypes } from "@/utility/strapi/types";

export default async function StatusPage() {
  const statuses = await findMany(ECollectionTypes.status, {});

  return (
    <div className="container mx-auto">
      <h1 className="mb-8 text-3xl font-bold">Status</h1>

      <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-white-dark">
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="border-b border-grey dark:border-primary">
                <th className="p-3 text-left">ID</th>
                <th className="p-3 text-left">Name</th>
                <th className="p-3 text-left">Created At</th>
              </tr>
            </thead>
            <tbody>
              {toArray(statuses.data).map((status, index) => (
                <tr
                  key={index}
                  className="border-b border-grey transition-apple hover:bg-primary-hover dark:border-primary dark:hover:bg-white-hover"
                >
                  <td className="p-3">{status.id}</td>
                  <td className="p-3">{status.name}</td>
                  <td className="p-3">{status.createdAt ? new Date(status.createdAt).toLocaleDateString() : 'N/A'}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
