import { FC, ReactNode, useCallback, useMemo } from "react";
import { RegisterOptions, useFormContext } from "react-hook-form";
import { FormValidationFunction } from "@/components/Form/utility/types";
import {
  emojiValidation,
  emptyValidation,
  fieldIsInvalid,
  requiredResolver
} from "@/components/Form/utility/FormValidationUtility";
import { sanitizeId } from "@/utility/accessability/AccessabilityUtility";

export interface FormStringInputProps {
  disabled?: boolean;
  readonly?: boolean;
  required?: RegisterOptions['required'];
  name: string;
  label: string;
  maxLength?: number;
  type?: 'input' | 'textarea';
  /** will render input type hidden instead */
  hidden?: boolean;
  className?: string;
  labelClassName?: string;
  validate?: FormValidationFunction;
  infoText?: string | ReactNode;
  placeholder?: string;
  allowEmoji?: boolean;
}

export const FormStringInput: FC<FormStringInputProps> = ({
  className = '',
  labelClassName = '',
  disabled = false,
  readonly = false,
  required = false,
  label,
  name,
  type: CustomTag = 'input',
  hidden = false,
  maxLength = CustomTag === 'input' ? 255 : 999, // 30 for input, 999 for textarea
  validate,
  infoText,
  placeholder = label,
  allowEmoji = true,
}) => {
  const { register, formState: {errors} } = useFormContext();
  const renderInfoText = useMemo(() => {
    if (typeof infoText === 'string') {
      return <small>{infoText}</small>;
    }
    return infoText;
  }, [infoText]);

  const invalid = fieldIsInvalid(errors, name);

  const getRegistrationOptions = useCallback((): RegisterOptions => {
    const validationRules: RegisterOptions = {
      disabled: disabled,
      required: requiredResolver(disabled || readonly, label, required),
    };

    if (maxLength !== undefined) {
      validationRules.maxLength = {
        value: maxLength,
        message: `${label} darf nicht länger als ${maxLength} Zeichen lang sein`,
      };
    }

    // Validierungsfunktionen in der validate-Eigenschaft
    validationRules.validate = {
      // Führe die übergebene Validierungsfunktion nur aus, wenn das Feld weder deaktiviert noch schreibgeschützt ist
      disabledValidator: validate && !(disabled || readonly) ? validate : emptyValidation,
      // Führe die Emoji-Validierung nur aus, wenn Emojis nicht erlaubt sind
      emojiValidation: allowEmoji ? emptyValidation : emojiValidation,
    };

    return validationRules;
  }, [disabled, readonly, label, required, maxLength, validate, allowEmoji]);

  return (
    <label
      className={`w-full ${
        hidden ? 'd-none' : 'd-flex'
      } gap-1 flex-column ${labelClassName}`}
    >
      {`${label}${required ? ' *' : ''}`}
      <CustomTag
        hidden={hidden}
        maxLength={maxLength}
        id={sanitizeId(`${name}-${label}`)}
        type="text"
        placeholder={placeholder || ""}
        // @ts-ignore
        name={name}
        disabled={disabled}
        readOnly={readonly}
        className={`w-100 form-control ${invalid ? ' is-invalid' : ''} ${className}`}
        aria-invalid={invalid ? 'true' : 'false'}
        rows={CustomTag === 'textarea' ? 4 : undefined}
        {...register(name, getRegistrationOptions())}
      />
      {renderInfoText}
    </label>
  );
};
