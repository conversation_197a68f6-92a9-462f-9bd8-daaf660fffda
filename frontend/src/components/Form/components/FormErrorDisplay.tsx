import { FC } from "react";
import { useFormContext } from "react-hook-form";
import { EventFormData } from "@/components/Event/Form/EventForm";


export const FormErrorDisplay: FC = () => {

  const { formState: { errors } } = useFormContext<EventFormData>();

  if (Object.keys(errors).length === 0) {
    return null;
  }

  return (
    <div className="rounded-lg  p-4 border-highlight border-2" role="alert" aria-live="assertive">
      <h3 className="text-sm font-medium text-color-highlight mb-2">
        Bitte korrigieren Sie folgende Fehler:
      </h3>
      <ul className="text-sm text-color-highlight space-y-1" role="list">
        {Object.entries(errors).map(([field, error]) => (
          <li key={field}>
            {error?.message || `<PERSON><PERSON> im Feld ${field}`}
          </li>
        ))}
      </ul>
    </div>
  );
};
