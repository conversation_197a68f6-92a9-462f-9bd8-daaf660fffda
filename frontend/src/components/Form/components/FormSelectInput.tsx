import { FC, useCallback } from "react";
import { RegisterOptions, useFormContext } from "react-hook-form";
import { fieldIsInvalid, requiredResolver } from "@/components/Form/utility/FormValidationUtility";
import { sanitizeId } from "@/utility/accessability/AccessabilityUtility";
import { FormValidationFunction } from "@/components/Form/utility/types";

export interface SelectOption {
  value: string | number;
  label: string;
}

interface Props {
  disabled?: boolean;
  readonly?: boolean;
  required?: boolean;
  hidden?: boolean;
  name: string;
  label: string;
  options: SelectOption[];
  className?: string;
  labelClass?: string;
  validate?: FormValidationFunction;
  placeholder?: string;
  multiple?: boolean;
}

export const FormSelectInput: FC<Props> = ({
  className = "",
  labelClass = "w-100",
  disabled = false,
  readonly = false,
  required = false,
  label,
  name,
  options,
  validate,
  hidden = false,
  placeholder,
  multiple = false
}) => {
  // Auto-generate placeholder if not provided
  const autoPlaceholder = placeholder || `${label} auswählen...`;
  const { register, formState: { errors } } = useFormContext();

  const invalid = fieldIsInvalid(errors, name);

  const getRegistrationOptions = useCallback((): RegisterOptions => {
    const validationRules: RegisterOptions = {
      disabled: disabled,
      required: requiredResolver(disabled || readonly, label, required),
    };

    if (validate && !(disabled || readonly)) {
      validationRules.validate = validate;
    }

    return validationRules;
  }, [disabled, readonly, label, required, validate]);

  return (
    <label className={`${labelClass} ${hidden ? "d-none" : "d-flex"} gap-1 flex-column`}>
      {`${label}${required ? " *" : ""}`}
      <select
        hidden={hidden}
        id={sanitizeId(`${name}-${label}`)}
        disabled={disabled || readonly}
        multiple={false}
        className={`w-100 form-control ${invalid ? " is-invalid" : ""} ${className}`}
        aria-invalid={invalid ? "true" : "false"}
        {...register(name, getRegistrationOptions())}
      >
        {!multiple && (
          <option value="" disabled>
            {autoPlaceholder}
          </option>
        )}
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </label>
  );
};
