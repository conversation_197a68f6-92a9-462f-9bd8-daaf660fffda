import { FC, useCallback } from "react";
import { RegisterOptions, useFormContext } from "react-hook-form";
import { fieldIsInvalid, requiredResolver } from "@/components/Form/utility/FormValidationUtility";
import { sanitizeId } from "@/utility/accessability/AccessabilityUtility";
import { FormValidationFunction } from "@/components/Form/utility/types";

interface Props {
  disabled?: boolean;
  readonly?: boolean;
  required?: boolean;
  hidden?: boolean;
  name: string;
  label: string;
  className?: string;
  labelClass?: string;
  validate?: FormValidationFunction;
  accept?: string; // File types to accept, e.g., ".pdf,.doc,.docx"
  multiple?: boolean;
}

export const FormFileInput: FC<Props> = ({
  className = "",
  labelClass = "w-100",
  disabled = false,
  readonly = false,
  required = false,
  label,
  name,
  validate,
  hidden = false,
  accept = ".pdf,.doc,.docx,.txt",
  multiple = false
}) => {
  const { register, formState: { errors } } = useFormContext();

  const invalid = fieldIsInvalid(errors, name);

  const getRegistrationOptions = useCallback((): RegisterOptions => {
    const disabledOrReadOnly = disabled || readonly;

    return {
      required: requiredResolver(disabledOrReadOnly, label, required),
      validate: validate || undefined,
    };
  }, [disabled, readonly, label, required, validate]);

  return (
    <label className={`w-full ${hidden ? "hidden" : "flex"} gap-1 flex-col ${labelClass}`}>
      {`${label}${required ? " *" : ""}`}
      <input
        hidden={hidden}
        id={sanitizeId(`${name}-${label}`)}
        type="file"
        accept={accept}
        multiple={multiple}
        disabled={disabled}
        readOnly={readonly}
        className={`w-full ${invalid ? "border-red-500" : ""} ${className}`}
        aria-invalid={invalid ? "true" : "false"}
        {...register(name, getRegistrationOptions())}
      />
      {accept && (
        <small className="text-xs text-grey dark:text-grey-hover">
          Erlaubte Dateiformate: {accept}
        </small>
      )}
    </label>
  );
};
