import { FC, useCallback } from "react";
import { RegisterOptions, useFormContext } from "react-hook-form";
import { fieldIsInvalid, requiredResolver } from "@/components/Form/utility/FormValidationUtility";
import { sanitizeId } from "@/utility/accessability/AccessabilityUtility";
import { FormValidationFunction } from "@/components/Form/utility/types";


interface Props {
  disabled?: boolean;
  readonly?: boolean;
  required?: boolean;
  /** will render input type hidden instead */
  hidden?: boolean;
  name: string;
  label: string;
  minValue?: number;
  maxValue?: number;
  className?: string;
  labelClass?: string;
  validate?: FormValidationFunction;
  placeholder?: string;
}

export const FormNumberInput: FC<Props> = ({
                                             className = "",
                                             labelClass = "w-100",
                                             disabled = false,
                                             readonly = false,
                                             required = false,
                                             label,
                                             name,
                                             minValue,
                                             maxValue,
                                             validate,
                                             hidden = false,
                                             placeholder = label
                                           }) => {
  const { register, formState: { errors } } = useFormContext();

  const invalid = fieldIsInvalid(errors, name);

  const getRegistrationOptions = useCallback((): RegisterOptions => {
    const validationRules: RegisterOptions = {
      valueAsNumber: true,
      disabled: disabled,
      required: requiredResolver(disabled || readonly, label, required),
    };

    if (maxValue !== undefined) {
      validationRules.max = {
        value: maxValue,
        message: `${label} darf nicht größer als ${maxValue} sein`
      };
    }

    if (minValue !== undefined) {
      validationRules.min = {
        value: minValue,
        message: `${label} darf nicht kleiner als ${minValue} sein`
      };
    }

    if (validate && !(disabled || readonly)) {
      validationRules.validate = validate;
    }

    return validationRules;
  }, [disabled, readonly, label, required, maxValue, minValue, validate]);

  return (
    <label className={`${labelClass} ${hidden ? "d-none" : "d-flex"} gap-1 flex-column`}>
      {`${label}${required ? " *" : ""}`}
      <input
        hidden={hidden}
        id={sanitizeId(`${name}-${label}`)}
        type="number"
        placeholder={placeholder || ""}
        disabled={disabled}
        readOnly={readonly}
        className={`w-100 form-control ${invalid ? " is-invalid" : ""} ${className}`}
        aria-invalid={invalid ? "true" : "false"}
        {...register(name, getRegistrationOptions())}
      />
    </label>
  );
};
