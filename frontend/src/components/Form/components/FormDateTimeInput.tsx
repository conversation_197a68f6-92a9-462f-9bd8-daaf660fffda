import { FC, useCallback } from "react";
import { RegisterOptions, useFormContext } from "react-hook-form";
import { fieldIsInvalid, requiredResolver } from "@/components/Form/utility/FormValidationUtility";
import { sanitizeId } from "@/utility/accessability/AccessabilityUtility";
import { FormValidationFunction } from "@/components/Form/utility/types";

interface Props {
  disabled?: boolean;
  readonly?: boolean;
  required?: boolean;
  hidden?: boolean;
  name: string;
  label: string;
  className?: string;
  labelClass?: string;
  validate?: FormValidationFunction;
  placeholder?: string;
  min?: string;
  max?: string;
  minFieldName?: string; // Name of field to use for min validation
}

export const FormDateTimeInput: FC<Props> = ({
  className = "",
  labelClass = "w-100",
  disabled = false,
  readonly = false,
  required = false,
  label,
  name,
  validate,
  hidden = false,
  placeholder = label,
  min,
  max,
  minFieldName
}) => {
  const { register, formState: { errors }, watch } = useFormContext();

  // Auto-generate placeholder if not provided
  const autoPlaceholder = placeholder || `${label} auswählen...`;

  // Watch the min field if specified
  const minFieldValue = minFieldName ? watch(minFieldName) : undefined;
  const computedMin = min || (minFieldValue ? (typeof minFieldValue === 'string' ? minFieldValue : new Date(minFieldValue).toISOString().slice(0, 16)) : undefined);

  const invalid = fieldIsInvalid(errors, name);

  const getRegistrationOptions = useCallback((): RegisterOptions => {
    const validationRules: RegisterOptions = {
      disabled: disabled,
      required: requiredResolver(disabled || readonly, label, required),
    };

    if (computedMin) {
      validationRules.min = {
        value: computedMin,
        message: `${label} darf nicht vor ${computedMin} liegen`
      };
    }

    if (max) {
      validationRules.max = {
        value: max,
        message: `${label} darf nicht nach ${max} liegen`
      };
    }

    // Add automatic min field validation
    if (minFieldName && !(disabled || readonly)) {
      const customValidate = (value: string) => {
        if (value && minFieldValue && new Date(value) <= new Date(minFieldValue)) {
          return `${label} muss nach dem ${minFieldName} liegen`;
        }
        return true;
      };

      if (validate) {
        validationRules.validate = {
          custom: validate,
          minField: customValidate
        };
      } else {
        validationRules.validate = customValidate;
      }
    } else if (validate && !(disabled || readonly)) {
      validationRules.validate = validate;
    }

    return validationRules;
  }, [disabled, readonly, label, required, computedMin, max, validate, minFieldName, minFieldValue]);

  return (
    <label className={`${labelClass} ${hidden ? "d-none" : "d-flex"} gap-1 flex-column`}>
      {`${label}${required ? " *" : ""}`}
      <input
        hidden={hidden}
        id={sanitizeId(`${name}-${label}`)}
        type="datetime-local"
        placeholder={autoPlaceholder}
        disabled={disabled}
        readOnly={readonly}
        min={computedMin}
        max={max}
        className={`w-100 form-control ${invalid ? " is-invalid" : ""} ${className}`}
        aria-invalid={invalid ? "true" : "false"}
        {...register(name, getRegistrationOptions())}
      />
    </label>
  );
};
