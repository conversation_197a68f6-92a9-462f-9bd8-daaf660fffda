import { get, isBoolean } from "lodash";
import { FieldErrors, RegisterOptions } from "react-hook-form";
import { FormValidationFunction } from "@/components/Form/utility/types";
import emojiRegex from "emoji-regex";

export const fieldIsInvalid = (
  errors: FieldErrors<Record<string, unknown>>,
  name: string,
): boolean => {
  // get from lodash can search nested values like test.1.name in the object
  return get(errors, name) !== undefined;
};

export const requiredResolver = (
  disabledOrReadOnly: boolean,
  label: string,
  requiredFunc: RegisterOptions['required'],
): RegisterOptions['required'] => {
  if (isBoolean(requiredFunc)) {
    // if required and not disabled or readonly
    if (disabledOrReadOnly || !requiredFunc) {
      return;
    }
    return `${label} darf nicht leer sein`;
  }
  if (requiredFunc) {
    return requiredFunc;
  }
  return;
};

export const emojiValidation: FormValidationFunction = value => {
  const regex = emojiRegex();
  let match;
  while ((match = regex.exec(`${value}`))) {
    return `Invalides Zeichen ${match[0]}`;
  }
  return true;
};

// used when you have validator as a record, but you actually have a validation conditional
export const emptyValidation = () => true;