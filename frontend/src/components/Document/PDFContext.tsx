"use client";

import { createContext, ReactNode, RefObject, useContext, useRef, useState } from "react";
import { GhostHighlight, Highlight } from "@/lib/PDFReader/types";
import { PdfHighlighterUtils } from "@/lib/PDFReader/contexts/PdfHighlighterContext";
import type { PDFDocumentProxy } from "pdfjs-dist";

export interface PDFContextType {
  // Highlight state
  highlights: Highlight[];
  setHighlights: (highlights: Highlight[]) => void;
  addHighlight: (highlight: GhostHighlight, comment?: string) => void;
  removeHighlight: (id: string) => void;
  updateHighlight: (id: string, updatedData: Partial<Highlight>) => void;
  isViewerReady: boolean;
  setIsViewerReady: (isReady: boolean) => void;

  // Active highlight state for hover effects
  activeHighlightId: string | null;
  setActiveHighlightId: (id: string | null) => void;
  hoveredHighlightId: string | null;
  setHoveredHighlightId: (id: string | null) => void;

  // Zoom state
  zoom: number;
  setZoom: (zoom: number) => void;

  // Page state
  currentPage: number;
  totalPages: number;
  setCurrentPage: (page: number) => void;
  setTotalPages: (pages: number) => void;

  // Highlight mode
  highlightMode: boolean;
  toggleHighlightMode: () => void;

  // Sidebar state
  isSidebarOpen: boolean;
  toggleSidebar: () => void;
  sidebarMode: 'outline' | 'thumbnails';
  setSidebarMode: (mode: 'outline' | 'thumbnails') => void;
  outline: any[] | null;
  setOutline: (outline: any[] | null) => void;

  // PDF Highlighter Utils
  highlighterUtilsRef: RefObject<PdfHighlighterUtils>;
  pdfDocumentRef: RefObject<PDFDocumentProxy>;
}

export const PDFContext = createContext<PDFContextType | undefined>(undefined);

export function PDFProvider({ children }: { children: ReactNode }) {
  const [isViewerReady, setIsViewerReady] = useState(false);
  const [highlights, setHighlights] = useState<Highlight[]>([]);
  const [zoom, setZoom] = useState<number>(1);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [highlightMode, setHighlightMode] = useState<boolean>(true);
  const [activeHighlightId, setActiveHighlightId] = useState<string | null>(null);
  const [hoveredHighlightId, setHoveredHighlightId] = useState<string | null>(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(false);
  const [sidebarMode, setSidebarMode] = useState<'outline' | 'thumbnails'>('outline');
  const [outline, setOutline] = useState<any[] | null>(null);
  const highlighterUtilsRef = useRef<PdfHighlighterUtils>(null);
  const pdfDocumentRef = useRef<PDFDocumentProxy>(null);

  // Helper function to generate a unique ID
  const getNextId = () => String(Math.random()).slice(2);

  const addHighlight = (ghostHighlight: GhostHighlight, comment: string = "") => {
    const newHighlight: Highlight = {
      ...ghostHighlight,
      id: getNextId(),
      comment: comment
    };

    setHighlights((prevHighlights) => [newHighlight, ...prevHighlights]);
  };

  const removeHighlight = (id: string) => {
    setHighlights((prevHighlights) =>
      prevHighlights.filter((highlight) => highlight.id !== id)
    );
  };

  // Function to update an existing highlight
  const updateHighlight = (id: string, updatedData: Partial<Highlight>) => {
    console.log('Updating highlight:', id, updatedData);
    setHighlights((prevHighlights) => {
      const newHighlights = prevHighlights.map((highlight) => {
        if (highlight.id === id) {
          // Create a deep copy of the highlight to avoid reference issues
          const updatedHighlight = { ...highlight };

          // Update content if provided
          if (updatedData.content) {
            updatedHighlight.content = {
              ...updatedHighlight.content,
              ...updatedData.content
            };
          }

          // Update position if provided
          if (updatedData.position) {
            console.log('Updating position with:', updatedData.position);
            // Make sure we're updating the position correctly
            // Check if this is a complete position object with boundingRect and rects
            if (typeof updatedData.position === 'object' && 'boundingRect' in updatedData.position && 'rects' in updatedData.position) {
              // This is a complete ScaledPosition object, use it directly
              updatedHighlight.position = updatedData.position;
              console.log('Updated position with complete ScaledPosition object:', updatedHighlight.position);
            }
            // Check if this is a complete LTWHP object
            else if (typeof updatedData.position === 'object' &&
                'left' in updatedData.position &&
                'top' in updatedData.position &&
                'width' in updatedData.position &&
                'height' in updatedData.position &&
                'pageNumber' in updatedData.position) {
              // This is a complete LTWHP object, convert it to Scaled format
              const ltwhp = updatedData.position;
              const scaledRect = {
                x1: ltwhp.left,
                y1: ltwhp.top,
                x2: ltwhp.left + ltwhp.width,
                y2: ltwhp.top + ltwhp.height,
                width: updatedHighlight.position.boundingRect.width || 1000, // Use existing width or default
                height: updatedHighlight.position.boundingRect.height || 1000, // Use existing height or default
                pageNumber: ltwhp.pageNumber
              };

              // Update the position with the converted Scaled format
              updatedHighlight.position = {
                ...updatedHighlight.position,
                boundingRect: scaledRect
              };
              console.log('Updated position with converted Scaled format:', updatedHighlight.position);
            } else {
              // This is a partial position update, merge it carefully
              console.warn('Partial position updates are not supported, skipping');
            }
          }

          // Update any other fields
          return updatedHighlight;
        }
        return highlight;
      });
      console.log('New highlights:', newHighlights);
      return newHighlights;
    });
  };

  const toggleHighlightMode = () => {
    setHighlightMode((prev) => !prev);
  };

  const toggleSidebar = () => {
    setIsSidebarOpen((prev) => !prev);
  };

  return <PDFContext.Provider value={{
    highlights,
    setHighlights,
    addHighlight,
    removeHighlight,
    updateHighlight,
    zoom,
    setZoom,
    currentPage,
    totalPages,
    setCurrentPage,
    setTotalPages,
    highlightMode,
    toggleHighlightMode,
    highlighterUtilsRef,
    isViewerReady,
    setIsViewerReady,
    pdfDocumentRef,
    activeHighlightId,
    setActiveHighlightId,
    hoveredHighlightId,
    setHoveredHighlightId,
    isSidebarOpen,
    toggleSidebar,
    sidebarMode,
    setSidebarMode,
    outline,
    setOutline
  }}>{children}</PDFContext.Provider>;
}

export function usePDFContext() {
  const context = useContext(PDFContext);
  if (context === undefined) {
    throw new Error("usePDFContext must be used within a PDFProvider");
  }
  return context;
}
