'use client';

import React from "react";
import { PDFDocumentProxy } from "pdfjs-dist";
import { usePDFContext } from "./PDFContext";
import { FiSidebar, FiMinus, FiPlus, FiRefreshCw, FiEdit3 } from "react-icons/fi";

interface PDFControlsProps {
  pdfDocument: PDFDocumentProxy | null;
}

export default function PDFControls({
  pdfDocument
}: PDFControlsProps) {
  const {
    currentPage,
    totalPages,
    setCurrentPage,
    zoom,
    setZoom,
    highlightMode,
    toggleHighlightMode,
    highlighterUtilsRef,
    isSidebarOpen,
    toggleSidebar
  } = usePDFContext();


  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handleZoomIn = () => {
    setZoom(Math.min(zoom + 0.25, 3));
  };

  const handleZoomOut = () => {
    setZoom(Math.max(zoom - 0.25, 0.5));
  };

  const handleZoomReset = () => {
    setZoom(1);
  };

  const handlePageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const pageNumber = parseInt(e.target.value);
    if (!isNaN(pageNumber) && pageNumber >= 1 && pageNumber <= totalPages) {
      console.log("xxx")
      highlighterUtilsRef.current?.getViewer()?.scrollPageIntoView({
        pageNumber,
      })
    }
    setCurrentPage(pageNumber);

  };

  return (
    <div className="flex items-center justify-between rounded-lg bg-white p-2 shadow-sm dark:bg-white-dark">
      <div className="flex items-center space-x-2">
        <button
          onClick={toggleSidebar}
          className={`rounded-md p-2 text-primary transition-colors ${isSidebarOpen ? 'bg-accent/20 hover:bg-accent/30 dark:bg-accent-dark/70 dark:text-white dark:hover:bg-accent-dark/60' : 'bg-grey-hover hover:bg-grey dark:bg-primary dark:text-grey-hover dark:hover:bg-grey'}`}
          aria-label="Toggle Sidebar"
          title="Seitenleiste ein-/ausblenden"
        >
          <FiSidebar className="h-5 w-5" />
        </button>
        <div className="flex items-center">
          <input
            type="number"
            min={1}
            max={totalPages}
            value={currentPage}
            onChange={handlePageInputChange}
            className="w-12 rounded-md border border-grey p-1 text-center dark:border-grey dark:bg-primary dark:text-white"
          />
          <span className="mx-1 text-grey dark:text-grey-hover">von</span>
          <span className="text-grey dark:text-grey-hover">{totalPages}</span>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <button
          onClick={handleZoomOut}
          className="rounded-md bg-grey-hover p-2 text-primary transition-colors hover:bg-grey dark:bg-primary dark:text-grey-hover dark:hover:bg-grey"
          aria-label="Zoom Out"
          title="Verkleinern"
        >
          <FiMinus className="h-5 w-5" />
        </button>

        <span className="text-sm text-grey dark:text-grey-hover">{Math.round(zoom * 100)}%</span>

        <button
          onClick={handleZoomIn}
          className="rounded-md bg-grey-hover p-2 text-primary transition-colors hover:bg-grey dark:bg-primary dark:text-grey-hover dark:hover:bg-grey"
          aria-label="Zoom In"
          title="Vergrößern"
        >
          <FiPlus className="h-5 w-5" />
        </button>

        <button
          onClick={handleZoomReset}
          className="rounded-md bg-grey-hover p-2 text-primary transition-colors hover:bg-grey dark:bg-primary dark:text-grey-hover dark:hover:bg-grey"
          aria-label="Reset Zoom"
          title="Zoom zurücksetzen"
        >
          <FiRefreshCw className="h-5 w-5" />
        </button>

        <button
          onClick={toggleHighlightMode}
          className={`rounded-md p-2 text-primary transition-colors ${highlightMode ? 'bg-warning/20 hover:bg-warning/30 dark:bg-warning-dark/70 dark:text-white dark:hover:bg-warning-dark/60' : 'bg-grey-hover hover:bg-grey dark:bg-white-dark dark:text-grey-dark dark:hover:bg-white-hover'}`}
          aria-label="Toggle Highlight Mode"
          title="Markierungsmodus ein-/ausschalten"
        >
          <FiEdit3 className="h-5 w-5" />
        </button>
      </div>
    </div>
  );
}
