"use client";

import React, { useState } from "react";

interface HighlightTipProps {
  onConfirm: (comment: string) => void;
  onCancel: () => void;
}

export default function HighlightTip({ onConfirm, onCancel }: HighlightTipProps) {
  const [comment, setComment] = useState('');

  const handleConfirm = () => {
    onConfirm(comment);
  };

  return (
    <div className="flex flex-col bg-white p-3 rounded-md shadow-md dark:bg-white-dark border border-grey dark:border-primary min-w-[250px]">
      <textarea
        className="w-full p-2 mb-2 border border-grey rounded-md dark:border-grey dark:bg-white-dark dark:text-white"
        placeholder="Kommentar hinzufügen (optional)"
        value={comment}
        onChange={(e) => setComment(e.target.value)}
        rows={2}
      />
      <div className="flex justify-end space-x-2">
        <button
          onClick={onCancel}
          className="bg-grey-hover text-primary px-3 py-1 rounded-md hover:bg-primary-hover dark:bg-primary dark:text-primary-hover dark:hover:bg-grey"
        >
          Abbrechen
        </button>
        <button
          onClick={handleConfirm}
          className="bg-accent text-white px-3 py-1 rounded-md hover:bg-accent-hover dark:bg-accent-dark dark:hover:bg-accent-hover"
        >
          Speichern
        </button>
      </div>
    </div>
  );
}
