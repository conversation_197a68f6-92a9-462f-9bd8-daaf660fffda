'use client';

import { Message } from "@/types/auto_generated/message";
import { formatDate } from "@/utility/date/DateUtility";

interface DocumentCommentListProps {
  messages: Message[];
}

export default function DocumentCommentList({ messages }: DocumentCommentListProps) {
  if (!messages || messages.length === 0) {
    return (
      <div className="rounded-lg border border-grey p-4 text-center dark:border-primary">
        <p className="text-grey dark:text-grey-hover">Noch keine Kommentare. Se<PERSON> der Erste, der einen Kommentar hinterlässt!</p>
      </div>
    );
  }

  // Helper function to get comment type from message text
  const getCommentType = (message: Message): string => {
    if (!message.text) return 'Comment';
    if (message.text.includes('Major Issue')) return 'Major Issue';
    if (message.text.includes('Minor Issue')) return 'Minor Issue';
    if (message.text.includes('Typo')) return 'Typo';
    return message.type || 'Comment';
  };

  // Helper function to clean message text (remove position data)
  const cleanMessageText = (text: string | undefined): string => {
    if (!text) return '';
    return text.replace(/\n\nPosition: Page \d+, X: [\d.]+%, Y: [\d.]+%/, '');
  };

  // Helper function to get badge color based on comment type
  const getBadgeClasses = (type: string): string => {
    switch (type) {
      case 'Major Issue':
        return 'bg-highlight/10 text-highlight dark:bg-highlight-dark/30 dark:text-highlight-hover';
      case 'Minor Issue':
        return 'bg-primary/10 text-primary dark:bg-primary-dark/30 dark:text-primary-hover';
      case 'Typo':
        return 'bg-warning/10 text-warning dark:bg-warning-dark/30 dark:text-warning-hover';
      default:
        return 'bg-accent/10 text-accent dark:bg-accent-dark/30 dark:text-accent-hover';
    }
  };

  // Helper function to get emoji based on comment type
  const getEmoji = (type: string): string => {
    switch (type) {
      case 'Major Issue':
        return '🔴';
      case 'Minor Issue':
        return '🟠';
      case 'Typo':
        return '🟢';
      default:
        return '🔵';
    }
  };

  return (
    <div className="flex flex-col gap-4">
      <h3 className="text-lg font-semibold text-white" id="comments">Kommentare</h3>

      <div className="flex flex-col gap-3" role="list" aria-labelledby="comments">
        {messages.map((message, index) => {
          const commentType = getCommentType(message);
          const badgeClasses = getBadgeClasses(commentType);

          return (
            <div
              key={index}
              className="rounded-lg border border-grey p-4 dark:border-primary"
            >
              <div className="mb-2 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className={`rounded-full px-2.5 py-0.5 text-xs font-medium ${badgeClasses}`}>
                    {getEmoji(commentType)} {commentType}
                  </span>
                  {message.anonymous_email && (
                    <span className="text-sm text-grey dark:text-grey-dark">
                      {message.anonymous_email}
                    </span>
                  )}
                </div>
                <span className="text-xs text-grey dark:text-grey-dark">
                  {message.createdAt ? formatDate(new Date(message.createdAt)) : 'Unbekanntes Datum'}
                </span>
              </div>

              <p className="whitespace-pre-wrap text-sm">
                {cleanMessageText(message.text)}
              </p>
            </div>
          );
        })}
      </div>
    </div>
  );
}
