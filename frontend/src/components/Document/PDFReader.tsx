"use client";

import { useEffect } from "react";
import { Message } from "@/types/auto_generated/message";
import { DocumentPosition } from "./DocumentCommentForm";
import { PdfLoader } from "@/lib/PDFReader/components/PdfLoader";
import { PdfHighlighter } from "@/lib/PDFReader/components/PdfHighlighter";
import { Highlight, PdfSelection } from "@/lib/PDFReader/types";
import { PDFProvider, usePDFContext } from "./PDFContext";
import PDFControls from "./PDFControls";
import HighlightTip from "./HighlightTip";
import HighlightsSidebar from "./HighlightsSidebar";
import HighlightRenderer from "./HighlightRenderer";
import PDFSidebar from "./PDFSidebar";

interface PDFHighlighterProps {
  fileUrl: string;
  messages?: Message[];
  onPositionSelected?: (position: DocumentPosition) => void;
  eventId?: string;
  documentId?: string;
}

// Helper function to generate a unique ID
const getNextId = () => String(Math.random()).slice(2);

function PDFReaderContent({
  fileUrl,
  messages,
  onPositionSelected,
  eventId,
  documentId
}: PDFHighlighterProps) {
  const {
    highlights,
    addHighlight,
    updateHighlight,
    zoom,
    setTotalPages,
    highlightMode,
    currentPage,
    setCurrentPage,
    highlighterUtilsRef,
    isViewerReady,
    pdfDocumentRef,
    activeHighlightId,
    setActiveHighlightId,
    isSidebarOpen
  } = usePDFContext();

  /**
   * The highlighterUtilsRef is now provided by the PDFContext
   * It contains numerous helpful functions, such as scrollToHighlight,
   * getCurrentSelection, setTip, and many more
   *
   * We'll use the selection from the highlighterUtils instead of maintaining our own state
   */

  // Construct the full URL for the PDF file
  const fullFileUrl = fileUrl.startsWith('http')
    ? fileUrl
    : `${process.env.NEXT_PUBLIC_STRAPI_URL || ''}${fileUrl}`;

  // Handle selection based on highlight mode
  const handleSelection = (selection: PdfSelection) => {
    console.log('Selection made:', selection);
    // The selection is already set in the PdfHighlighter component
    // We just need to ensure the tip is visible at the right position
    if (highlighterUtilsRef.current) {
      const { position } = selection;
      highlighterUtilsRef.current.setTip({
        position: {
          boundingRect: position.boundingRect,
          rects: position.rects
        },
        content: highlighterUtilsRef.current.getTip()?.content
      });
    }
  };

  // Handle confirm from the highlight tip
  const handleConfirmHighlight = (comment: string) => {
    console.log('Creating highlight with comment:', comment);
    if (highlighterUtilsRef.current && highlighterUtilsRef.current.selection) {
      const selection = highlighterUtilsRef.current.selection;
      const ghostHighlight = selection.makeGhostHighlight();
      addHighlight(ghostHighlight, comment);

      // Hide the tip - this is already handled by makeGhostHighlight which calls clearTextSelection
      if (highlighterUtilsRef.current) {
        highlighterUtilsRef.current.setTip(null);
      }
    }
  };

  // Handle cancel from the highlight tip
  const handleCancelHighlight = () => {
    console.log("cancel highlight");

    // Clear the selection in the PdfHighlighter component
    if (highlighterUtilsRef.current) {
      // This will call clearTextSelection internally
      highlighterUtilsRef.current.setTip(null);
    }
  };

  // Scroll to a specific highlight
  const scrollToHighlight = (highlight: Highlight) => {
    if (highlighterUtilsRef.current) {
      highlighterUtilsRef.current.scrollToHighlight(highlight);
    }
  };




  // Update current page when scrolling
  useEffect(() => {
    if (pdfDocumentRef.current) {
      setTotalPages(pdfDocumentRef.current.numPages);
    }

    const viewer = highlighterUtilsRef.current?.getViewer();
    if (viewer) {
      console.log('Viewer:', viewer);

      const onPageChange = (event: any) => {
        console.log('Page changed:', event);
        if (event.pageNumber) {
          setCurrentPage(event.pageNumber);
        }
      }

      // Add page change event listener
      viewer.eventBus.on('pagechanging', onPageChange);

      // Initial page check

      return () => {
        if (viewer && viewer.container) {
          console.log("remove")
          // Remove page change event listener
          viewer.eventBus.off('pagechanging', onPageChange);
        }
      };
    }
  }, [isViewerReady, setTotalPages, setCurrentPage]);

  const context = usePDFContext();


  return (
    <div className="flex flex-col h-full">
      <PdfLoader documentUid={fileUrl} document={fullFileUrl} workerSrc="/pdf.worker5.js"

      >
        {(pdfDocument) => {

          return (
            <>
              <div className="mb-2">
                <PDFControls pdfDocument={pdfDocument} />
              </div>
              <div className="flex flex-grow relative h-[calc(100vh-8rem)]" style={{ minHeight: '600px' }}>
                <div
                  className={`overflow-auto border-r border-grey-hover dark:border-grey transition-all duration-300 ease-in-out ${
                    isSidebarOpen ? 'w-1/5 opacity-100 translate-x-0' : 'w-0 opacity-0 -translate-x-full'
                  }`}
                  style={{
                    visibility: isSidebarOpen ? 'visible' : 'hidden',
                    transitionProperty: 'transform, opacity, width'
                  }}
                >
                  <div className="w-64">
                    <PDFSidebar />
                  </div>
                </div>
                <div className={`relative overflow-hidden transition-all duration-300 ease-in-out ${isSidebarOpen ? 'w-3/5' : 'w-3/4'}`}>
                  <PdfHighlighter
                    enableAreaSelection={(event) => event.altKey}
                    pdfDocument={pdfDocument}
                    utilsRef={(_pdfHighlighterUtils) => {
                      highlighterUtilsRef.current = _pdfHighlighterUtils;
                    }}
                    pdfScaleValue={zoom}
                    onSelection={handleSelection}
                    selectionTip={
                      <HighlightTip
                        onConfirm={handleConfirmHighlight}
                        onCancel={handleCancelHighlight}
                      />
                    }
                    highlights={highlights}
                  >
                    <HighlightRenderer />
                  </PdfHighlighter>
                </div>
                <div className="w-1/4 overflow-auto border-l border-grey-hover dark:border-grey">
                  <HighlightsSidebar onScrollToHighlight={scrollToHighlight} />
                </div>
              </div>
            </>
          );
        }}
      </PdfLoader>
    </div>
  );
}

export default function PDFReader(props: PDFHighlighterProps) {
  return (
    <PDFProvider>
      <PDFReaderContent {...props} />
    </PDFProvider>
  );
}
