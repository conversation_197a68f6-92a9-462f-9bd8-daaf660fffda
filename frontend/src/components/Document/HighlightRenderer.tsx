"use client";

import React from "react";
import { useHighlightContainerContext } from "@/lib/PDFReader/contexts/HighlightContext";
import { MonitoredHighlightContainer } from "@/lib/PDFReader/components/MonitoredHighlightContainer";
import { TextHighlight } from "@/lib/PDFReader/components/TextHighlight";
import { AreaHighlight } from "@/lib/PDFReader/components/AreaHighlight";
import { usePdfHighlighterContext } from "@/lib/PDFReader/contexts/PdfHighlighterContext";
import { PDFContextType, usePDFContext } from "@/components/Document/PDFContext";

interface Props {
  context: PDFContextType
}

export default function HighlightRenderer(
  //{ context }: Props
) {
  try {
    const {
      highlight, // The highlight being rendred
      viewportToScaled, // Convert a highlight position to platform agnostic coords (useful for saving edits)
      screenshot, // Screenshot a bounding rectangle
      isScrolledTo, // Whether the highlight has been auto-scrolled to
      highlightBindings, // Whether the highlight has been auto-scrolled to
    } = useHighlightContainerContext();

    const { toggleEditInProgress } = usePdfHighlighterContext();
    const { updateHighlight, setActiveHighlightId, activeHighlightId, hoveredHighlightId, setHoveredHighlightId } = usePDFContext();


    if (!highlight) {
      return <div className="empty-highlight" />;
    }

    const isHovered = highlight.id === hoveredHighlightId;

    // Render the appropriate highlight component based on type
    const renderHighlight = () => {
      if (highlight.type === 'area') {
        return (
          <AreaHighlight
            //style={customStyle}
            isScrolledTo={isScrolledTo}
            highlight={highlight}
            onChange={(boundingRect) => {
              // Create a new screenshot of the moved area
              const image = screenshot(boundingRect);

              // Create the scaled position
              const scaledPosition = viewportToScaled(boundingRect);

              // Create the update object
              const updates = {
                content: {
                  image: image
                },
                position: {
                  boundingRect: scaledPosition,
                  rects: []
                }
              };

              // Try both methods to update the highlight
              // 1. Direct update through context
              updateHighlight(highlight.id, updates);

              // End edit mode
              toggleEditInProgress(false);
            }}
            bounds={highlightBindings.textLayer}
            onEditStart={() => toggleEditInProgress(true)}
          />
        );
      }

      return (
        <TextHighlight
          highlight={highlight}
          isScrolledTo={isScrolledTo}
          //style={customStyle}
          //context={context}
        />
      );
    };

    return (
      <MonitoredHighlightContainer
        key={highlight.id}
        onMouseEnter={() => {
          setHoveredHighlightId(highlight.id);
        }}
        onMouseLeave={() => {
          setHoveredHighlightId(null);
        }}
        onClick={() => {
          console.log("clicked", highlight.id);
          setActiveHighlightId(highlight.id);
        }}
      >
        {renderHighlight()}
      </MonitoredHighlightContainer>
    );
  } catch (error) {
    console.error('Error in HighlightRenderer:', error);
    return <div className="error-highlight" />;
  }
}
