'use client';

import { FC } from "react";
import { useRouter } from "next/navigation";
import { DocumentForm, DocumentFormData } from "./DocumentForm";
import { getCollection } from "@/utility/strapi/StrapiUtility";
import { ECollectionTypes } from "@/utility/strapi/types";
import { FormProvider, useForm } from "react-hook-form";
import { getDocumentContext, getContextDisplayName } from "./DocumentFormContext";

interface DocumentFormWrapperProps {
  eventId?: string; // Event documentId from URL
  workingPackageId?: string; // Optional for future working package context
}

const defaultValues: DocumentFormData = {
  name: '',
  file: null,
}

export const DocumentFormWrapper: FC<DocumentFormWrapperProps> = ({
  eventId,
  workingPackageId
}) => {
  const router = useRouter();

  // Determine the context for this document upload
  const context = getDocumentContext(eventId, workingPackageId);

  const methods = useForm<DocumentFormData>({
    defaultValues,
  });

  const onSubmit = async (data: DocumentFormData) => {
    try {
      if (!data.file || data.file.length === 0) {
        alert('Bitte wählen Sie eine Datei aus.');
        return;
      }

      const file = data.file[0];

      // Use custom endpoint for atomic document upload
      const formData = new FormData();
      formData.append('file', file);
      formData.append('name', data.name);

      const endpoint = context.type === 'event'
        ? `/api/events/${context.id}/add-document`
        : `/api/working-packages/${context.id}/add-document`; // Future implementation

      const response = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_URL}${endpoint}`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Document upload failed');
      }

      const result = await response.json();
      console.log(`Document uploaded and ${context.type} updated:`, result);

      // Redirect back to the parent page
      router.push(context.redirectPath);

    } catch (error) {
      console.error('Error uploading document:', error);
      alert('Fehler beim Hochladen des Dokuments. Bitte versuchen Sie es erneut.');
    }
  };

  return (
    <FormProvider {...methods}>
      <div className="max-w-4xl mx-auto p-6 text-white">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">
            Neues Dokument hochladen
          </h1>
          <p className="mt-2">
            Laden Sie ein neues Dokument für dieses {getContextDisplayName(context.type)} hoch. Das Dokument wird automatisch konvertiert und steht für die Überprüfung zur Verfügung.
          </p>
        </div>

        <div className="rounded-lg shadow-sm p-6">
          <form onSubmit={methods.handleSubmit(onSubmit)} className="flex flex-col gap-4">
            <DocumentForm submitButtonText="Dokument hochladen" />
          </form>
        </div>
      </div>
    </FormProvider>
  );
};
