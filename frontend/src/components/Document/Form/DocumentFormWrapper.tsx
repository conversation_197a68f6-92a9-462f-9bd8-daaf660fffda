'use client';

import { FC } from "react";
import { useRouter } from "next/navigation";
import { DocumentForm, DocumentFormData } from "./DocumentForm";
import { getCollection } from "@/utility/strapi/StrapiUtility";
import { ECollectionTypes } from "@/utility/strapi/types";
import { FormProvider, useForm } from "react-hook-form";
import { getDocumentContext, getContextDisplayName } from "./DocumentFormContext";

interface DocumentFormWrapperProps {
  eventId?: string; // Event documentId from URL
  workingPackageId?: string; // Optional for future working package context
}

const defaultValues: DocumentFormData = {
  name: '',
  file: null,
}

export const DocumentFormWrapper: FC<DocumentFormWrapperProps> = ({
  eventId,
  workingPackageId
}) => {
  const router = useRouter();

  // Determine the context for this document upload
  const context = getDocumentContext(eventId, workingPackageId);

  const methods = useForm<DocumentFormData>({
    defaultValues,
  });

  const onSubmit = async (data: DocumentFormData) => {
    try {
      if (!data.file || data.file.length === 0) {
        alert('Bitte wählen Sie eine Datei aus.');
        return;
      }

      const file = data.file[0];

      // First, upload the file to Strapi
      const formData = new FormData();
      formData.append('files', file);
      formData.append('fileInfo', JSON.stringify({
        name: data.name,
        alternativeText: data.name,
        caption: data.name,
      }));

      const uploadResponse = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_URL}/api/upload`, {
        method: 'POST',
        body: formData,
      });

      if (!uploadResponse.ok) {
        throw new Error('File upload failed');
      }

      const uploadedFiles = await uploadResponse.json();
      const uploadedFile = uploadedFiles[0];

      // Now update the parent entity (event or working package) with the new document
      const collection = getCollection(
        context.type === 'event' ? ECollectionTypes.event : ECollectionTypes.working_package
      );

      // Get current entity data to append the new document
      const currentEntity = await collection.findOne(context.id, {
        populate: {
          documents: {
            fields: ["id", "name"],
            populate: {
              file: true,
              converted_file: true
            }
          }
        }
      });

      // Prepare existing documents
      const existingDocuments = currentEntity.data?.documents || [];
      const documentsData = existingDocuments.map((doc: any) => ({
        id: doc.id,
        name: doc.name,
        file: doc.file?.id,
        converted_file: doc.converted_file?.id
      }));

      // Add new document
      documentsData.push({
        name: data.name,
        file: uploadedFile.id,
      });

      // Update the entity
      const result = await collection.update(context.id, {
        documents: documentsData
      });

      console.log(`Document uploaded and ${context.type} updated:`, result);

      // Redirect back to the parent page
      router.push(context.redirectPath);

    } catch (error) {
      console.error('Error uploading document:', error);
      alert('Fehler beim Hochladen des Dokuments. Bitte versuchen Sie es erneut.');
    }
  };

  return (
    <FormProvider {...methods}>
      <div className="max-w-4xl mx-auto p-6 text-white">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">
            Neues Dokument hochladen
          </h1>
          <p className="mt-2">
            Laden Sie ein neues Dokument für dieses {getContextDisplayName(context.type)} hoch. Das Dokument wird automatisch konvertiert und steht für die Überprüfung zur Verfügung.
          </p>
        </div>

        <div className="rounded-lg shadow-sm p-6">
          <form onSubmit={methods.handleSubmit(onSubmit)} className="flex flex-col gap-4">
            <DocumentForm submitButtonText="Dokument hochladen" />
          </form>
        </div>
      </div>
    </FormProvider>
  );
};
