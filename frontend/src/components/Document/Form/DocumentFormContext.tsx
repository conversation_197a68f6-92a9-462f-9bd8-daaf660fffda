/**
 * Document Form Context Utilities
 * 
 * This file provides utilities for handling document uploads in different contexts:
 * - Event context: Documents belong to an event
 * - Working Package context: Documents belong to a working package (future implementation)
 */

export type DocumentContext = 'event' | 'working-package';

export interface DocumentContextInfo {
  type: DocumentContext;
  id: string;
  redirectPath: string;
}

/**
 * Determines the context for document upload based on the provided parameters
 */
export function getDocumentContext(
  eventId?: string,
  workingPackageId?: string
): DocumentContextInfo {
  if (eventId) {
    return {
      type: 'event',
      id: eventId,
      redirectPath: `/events/${eventId}`
    };
  }
  
  if (workingPackageId) {
    return {
      type: 'working-package',
      id: workingPackageId,
      redirectPath: `/working-packages/${workingPackageId}`
    };
  }
  
  throw new Error('Either eventId or workingPackageId must be provided');
}

/**
 * Gets the appropriate collection type for the context
 */
export function getContextCollectionType(context: DocumentContext) {
  switch (context) {
    case 'event':
      return 'events';
    case 'working-package':
      return 'working-packages';
    default:
      throw new Error(`Unknown context: ${context}`);
  }
}

/**
 * Gets the display name for the context
 */
export function getContextDisplayName(context: DocumentContext): string {
  switch (context) {
    case 'event':
      return 'Event';
    case 'working-package':
      return 'Working Package';
    default:
      return 'Unknown';
  }
}
