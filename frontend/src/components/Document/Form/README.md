# Document Upload System

This directory contains the document upload functionality that supports both Event and Working Package contexts.

## Components

### DocumentForm.tsx
The main form component that handles:
- Document name input
- File upload input
- Form validation and submission

### DocumentFormWrapper.tsx
The wrapper component that provides:
- Form state management with React Hook Form
- Context-aware document upload logic
- File upload to Strapi
- Entity update (Event or Working Package)
- Navigation after successful upload

### DocumentFormContext.tsx
Utility functions for handling different upload contexts:
- Context detection (Event vs Working Package)
- Collection type mapping
- Redirect path generation

### FormFileInput.tsx
Reusable file input component with:
- File type validation
- Required field support
- Tailwind CSS styling
- React Hook Form integration

## Usage

### Event Context (Current Implementation)
```tsx
// Page: /events/[slug]/documents/new/page.tsx
<DocumentFormWrapper eventId={eventSlug} />
```

### Working Package Context (Future Implementation)
```tsx
// Page: /working-packages/[slug]/documents/new/page.tsx
<DocumentFormWrapper workingPackageId={workingPackageSlug} />
```

## File Upload Flow

1. User selects file and enters document name
2. Form validates input
3. File is uploaded to Strapi upload endpoint
4. Parent entity (Event/Working Package) is fetched
5. New document is appended to existing documents array
6. Entity is updated with new documents array
7. User is redirected to parent entity page

## Backend Integration

The system integrates with:
- Strapi upload API (`/api/upload`)
- Event collection API (`/api/events`)
- Working Package collection API (`/api/working-packages`)

Documents are stored as `shared.media` components with:
- `name`: Document display name
- `file`: Uploaded file reference
- `converted_file`: Auto-generated PDF (via lifecycle hooks)

## Future Enhancements

To add Working Package document upload:

1. Create `/working-packages/[slug]/documents/new/page.tsx`
2. Add upload button to Working Package detail page
3. Use existing `DocumentFormWrapper` with `workingPackageId` prop

The system is already prepared for this extension through the context utilities.
