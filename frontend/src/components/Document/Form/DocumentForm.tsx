"use client";

import { FC } from "react";
import { useFormContext } from "react-hook-form";
import { FormStringInput } from "@/components/Form/components/FormStringInput";
import { FormFileInput } from "@/components/Form/components/FormFileInput";
import { FormErrorDisplay } from "@/components/Form/components/FormErrorDisplay";

// Form data type based on shared.media component structure
export type DocumentFormData = {
  name: string;
  file: FileList | null;
};

interface DocumentFormProps {
  submitButtonText?: string;
}

export const DocumentForm: FC<DocumentFormProps> = ({
  submitButtonText = "Dokument speichern"
}) => {
  const { formState: { errors, isSubmitting, isValid } } = useFormContext<DocumentFormData>();

  return (
    <>
      <FormErrorDisplay />
      
      <div className="flex flex-col gap-4">
        <FormStringInput
          name="name"
          label="Dokumentname"
          required={true}
          className="rounded-lg border border-color-grey p-3 dark:border-color-primary dark:bg-color-primary"
          labelClassName="text-sm font-medium text-color-black dark:text-color-white"
        />

        <FormFileInput
          name="file"
          label="Datei"
          required={true}
          accept=".pdf,.doc,.docx,.txt,.odt,.rtf"
          className="rounded-lg border border-color-grey p-3 dark:border-color-primary dark:bg-color-primary"
          labelClass="text-sm font-medium text-color-black dark:text-color-white"
        />
      </div>

      <div className="flex gap-3 pt-4">
        <button
          type="submit"
          disabled={isSubmitting}
          className={`flex-1 rounded-lg px-6 py-3 font-medium text-color-white border-2 border-primary ${
            isSubmitting ? 'opacity-50 cursor-not-allowed' : 'hover:bg-color-primary'
          }`}
        >
          {isSubmitting ? "Wird hochgeladen..." : submitButtonText}
        </button>
      </div>
    </>
  );
};
