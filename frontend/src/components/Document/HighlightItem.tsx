"use client";

import React from "react";
import { Highlight } from "@/lib/PDFReader/types";
import { usePDFContext } from "./PDFContext";

interface HighlightItemProps {
  highlight: Highlight;
  onScrollToHighlight: (highlight: Highlight) => void;
}

export default function HighlightItem({ highlight, onScrollToHighlight }: HighlightItemProps) {
  const { removeHighlight, activeHighlightId, setActiveHighlightId, hoveredHighlightId, setHoveredHighlightId } = usePDFContext();

  const isActive = activeHighlightId === highlight.id;
  const isHovered = hoveredHighlightId === highlight.id;

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    removeHighlight(highlight.id);
  };

  const handleClick = () => {
    onScrollToHighlight(highlight);
    setActiveHighlightId(highlight.id);
  };


  // Determine if this is a text or area highlight
  const isTextHighlight = Boolean(highlight.content?.text);
  const isAreaHighlight = Boolean(highlight.content?.image);

  // Get content based on highlight type
  const highlightContent = isTextHighlight
    ? highlight?.content?.text
    : isAreaHighlight
      ? 'Bereichsmarkierung'
      : 'Unbekannter Highlight-Typ';

  // Add a background color based on highlight type
  const highlightTypeClass = isTextHighlight
    ? 'border-l-4 border-l-warning'
    : 'border-l-4 border-l-accent';

  // Check if there's a comment
  const hasComment = Boolean(highlight.comment && highlight.comment.trim());

  // Apply active/hover styles
  const activeClass = isActive || isHovered
    ? 'bg-accent dark:bg-accent-dark border-highlight dark:border-highlight-dark shadow-md'
    : 'bg-white dark:bg-white-dark border-grey dark:border-primary';

  return (
    <div
      className={`p-3 mb-2 rounded-lg cursor-pointer border transition-all duration-200 ease-in-out ${activeClass} ${highlightTypeClass}`}
      onClick={handleClick}
      onMouseEnter={() => setHoveredHighlightId(highlight.id)}
      onMouseLeave={() => setHoveredHighlightId(null)}
    >
      <div className="flex justify-between items-start">
        <div className="flex-1">
          {isAreaHighlight && highlight.content?.image ? (
            <div className="mb-2">
              <div className="text-xs font-medium text-grey dark:text-grey-dark mb-1">Bereichsmarkierung:</div>
              <div className="border border-grey dark:border-primary rounded overflow-hidden" style={{ maxWidth: '100%', maxHeight: '150px' }}>
                <img
                  src={highlight.content.image}
                  alt="Bereichsmarkierung"
                  className="max-w-full h-auto object-contain"
                  style={{ maxHeight: '150px' }}
                />
              </div>
            </div>
          ) : (
            <p className="text-sm text-primary dark:text-grey-hover line-clamp-2">
              {highlightContent}
            </p>
          )}
          {hasComment && (
            <p className="text-xs italic text-grey dark:text-grey-hover mt-1 line-clamp-2">
              "{highlight.comment}"
            </p>
          )}
        </div>
        <button
          onClick={handleDelete}
          className="ml-2 text-grey-hover hover:text-highlight dark:text-grey dark:hover:text-highlight-hover"
          aria-label="Markierung löschen"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </button>
      </div>
    </div>
  );
}
