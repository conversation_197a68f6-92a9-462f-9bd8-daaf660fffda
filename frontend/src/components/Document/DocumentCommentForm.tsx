'use client';

import { useForm } from "react-hook-form";
import { createDocumentComment } from "@/app/events/[slug]/documents/[documentId]/actions";

export interface DocumentPosition {
  pageNumber: number;
  x: number;  // Percentage position on the page (0-100)
  y: number;  // Percentage position on the page (0-100)
}

export interface DocumentCommentFormData {
  text: string;
  type: 'Comment' | 'Major Issue' | 'Minor Issue' | 'Typo';
  anonymous_email?: string;
  position?: DocumentPosition;
}

interface DocumentCommentFormProps {
  eventId: string;
  documentId: string;
  position?: DocumentPosition;
  onCommentAdded?: () => void;
}

export default function DocumentCommentForm({
  eventId,
  documentId,
  position,
  onCommentAdded
}: DocumentCommentFormProps) {
  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { errors, isSubmitting, isSubmitSuccessful },
    setError,
  } = useForm<DocumentCommentFormData>({
    defaultValues: {
      text: '',
      type: 'Comment',
      anonymous_email: '',
      position: position,
    },
  });

  const commentType = watch('type');

  const onSubmit = async (data: DocumentCommentFormData) => {
    try {
      // Clean up the data
      const formData = {
        ...data,
        anonymous_email: data.anonymous_email && data.anonymous_email.trim() !== ''
          ? data.anonymous_email
          : undefined,
        position: position,
      };

      await createDocumentComment(eventId, documentId, formData);
      reset(); // Reset form after successful submission

      if (onCommentAdded) {
        onCommentAdded();
      }
    } catch (error) {
      console.error('Error submitting document comment:', error);
      setError('root.serverError', {
        type: 'server',
        message: 'Failed to submit your comment. Please try again.'
      });
    }
  };

  return (
    <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-white-dark">
      <h3 className="mb-4 text-lg font-semibold text-white">Kommentar hinzufügen</h3>

      {isSubmitSuccessful && (
        <div className="mb-4 rounded-lg bg-warning/10 p-3 text-warning dark:bg-warning-dark/20 dark:text-warning-hover">
          Dein Kommentar wurde erfolgreich übermittelt!
        </div>
      )}

      {errors.root?.serverError && (
        <div className="mb-4 rounded-lg bg-highlight/10 p-3 text-highlight dark:bg-highlight-dark/20 dark:text-highlight-hover">
          {errors.root.serverError.message}
        </div>
      )}

      {position && (
        <div className="mb-4 rounded-lg bg-accent/10 p-3 text-accent dark:bg-accent-dark/20 dark:text-accent-hover">
          Kommentar auf Seite {position.pageNumber}, Position: {position.x.toFixed(1)}%, {position.y.toFixed(1)}%
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-3">
        <div>
          <label htmlFor="comment-type" className="mb-1 block text-sm font-medium">
            Kommentar-Typ
          </label>
          <div className="flex flex-wrap gap-4">
            <label className="flex items-center gap-2">
              <input
                type="radio"
                value="Comment"
                {...register('type')}
                className="h-4 w-4 text-accent focus:ring-accent-hover"
              />
              <span>Kommentar</span>
            </label>
            <label className="flex items-center gap-2">
              <input
                type="radio"
                value="Major Issue"
                {...register('type')}
                className="h-4 w-4 text-highlight focus:ring-highlight-hover"
              />
              <span>Schwerwiegendes Problem</span>
            </label>
            <label className="flex items-center gap-2">
              <input
                type="radio"
                value="Minor Issue"
                {...register('type')}
                className="h-4 w-4 text-primary focus:ring-primary-hover"
              />
              <span>Kleineres Problem</span>
            </label>
            <label className="flex items-center gap-2">
              <input
                type="radio"
                value="Typo"
                {...register('type')}
                className="h-4 w-4 text-warning focus:ring-warning-hover"
              />
              <span>Tippfehler</span>
            </label>
          </div>
        </div>

        <div>
          <label htmlFor="anonymous_email" className="mb-1 block text-sm font-medium">
            Deine E-Mail (optional)
          </label>
          <input
            id="anonymous_email"
            type="email"
            placeholder="<EMAIL>"
            className="w-full rounded-lg border border-grey p-2 dark:border-grey dark:bg-primary"
            {...register('anonymous_email', {
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: 'Ungültige E-Mail-Adresse',
              },
            })}
          />
          {errors.anonymous_email && (
            <p className="mt-1 text-sm text-highlight dark:text-highlight-hover">{errors.anonymous_email.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="text" className="mb-1 block text-sm font-medium">
            Kommentar
          </label>
          <textarea
            id="text"
            rows={4}
            placeholder="Gib deinen Kommentar hier ein..."
            className="w-full rounded-lg border border-grey p-2 dark:border-grey dark:bg-white-dark"
            {...register('text', {
              required: 'Kommentartext ist erforderlich',
              minLength: {
                value: 3,
                message: 'Kommentar muss mindestens 3 Zeichen lang sein',
              },
            })}
          />
          {errors.text && (
            <p className="mt-1 text-sm text-highlight dark:text-highlight-hover">{errors.text.message}</p>
          )}
        </div>

        <button
          type="submit"
          disabled={isSubmitting}
          className={`mt-2 rounded-lg px-4 py-2 font-medium text-white transition-colors ${
            commentType === 'Major Issue'
              ? 'bg-highlight hover:bg-highlight-hover dark:bg-highlight-dark dark:hover:bg-highlight-hover'
              : commentType === 'Minor Issue'
                ? 'bg-primary hover:bg-primary-hover dark:bg-primary-dark dark:hover:bg-primary-hover'
                : commentType === 'Typo'
                  ? 'bg-warning hover:bg-warning-hover dark:bg-warning-dark dark:hover:bg-warning-hover'
                  : 'bg-accent hover:bg-accent-hover dark:bg-accent-dark dark:hover:bg-accent-hover'
          } ${isSubmitting ? 'opacity-70 cursor-not-allowed' : ''}`}
        >
          {isSubmitting ? 'Wird übermittelt...' : 'Kommentar absenden'}
        </button>
      </form>
    </div>
  );
}
