import { toArray } from "lodash";
import Link from "next/link";
import { Event } from "@/types/auto_generated/event";

interface DocumentListProps {
  event: Event;
}

export default function DocumentList({ event }: DocumentListProps) {
  const documents = toArray(event.documents);
  if (documents.length === 0) {
    return (
      <div className="rounded-lg border border-grey p-4 text-center dark:border-primary">
        <input type={"week"}/>
        <p className="text-grey dark:text-grey-hover">No documents available for this event.</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      <h3 className="text-lg font-semibold text-white" id="documents">Documents</h3>

      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3" role="list" aria-labelledby="documents">
        {documents.map((doc, index) => (
          <div
            key={index}
            className={`rounded-lg border p-4 transition-all border-grey  dark:border-primary `}
          >
            <div className="mb-2 flex items-center justify-between">
              <h4 className="font-medium text-white">{doc.name || `Document ${index + 1}`}</h4>
              <span className="text-xs text-grey dark:text-grey-dark">
                {doc.file?.mime || 'Unknown type'}
              </span>
            </div>

            <div className="mt-3 flex gap-2">
              {doc.file?.url && (
                <a
                  href={doc.file.url.startsWith('http') ? doc.file.url : `${process.env.NEXT_PUBLIC_STRAPI_URL || ''}${doc.file.url}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="rounded-md bg-black text-white hover:border-primary border-2 px-3 py-1.5 text-sm font-medium "
                >
                  View
                </a>
              )}
              <Link
                href={`/events/${event.documentId}/documents/${doc.id}`}
                className="rounded-md bg-black text-white hover:border-primary border-2 px-3 py-1.5 text-sm font-medium "
              >
                Review
              </Link>
            </div>

            <div className="mt-2 text-sm text-grey dark:text-grey-hover">
              {doc.messages?.length
                ? `${doc.messages.length} comments`
                : 'No comments yet'}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
