"use client";

import React from "react";
import { usePDFContext } from "./PDFContext";
import HighlightItem from "./HighlightItem";
import { Highlight } from "@/lib/PDFReader/types";

interface HighlightsSidebarProps {
  onScrollToHighlight: (highlight: Highlight) => void;
}

export default function HighlightsSidebar({ onScrollToHighlight }: HighlightsSidebarProps) {
  const { highlights, currentPage } = usePDFContext();

  // Filter highlights for the current page
  const currentPageHighlights = highlights.filter((highlight) => {
    // Check if position, boundingRect and pageNumber exist
    if (!highlight.position || !highlight.position.boundingRect || highlight.position.boundingRect.pageNumber === undefined) {
      console.warn('Highlight missing pageNumber:', highlight);
      return false;
    }
    return highlight.position.boundingRect.pageNumber === currentPage;
  });

  return (
    <div className="w-full h-full p-4 bg-grey-hover dark:bg-white-dark overflow-y-auto">
      <h3 className="text-lg font-medium mb-4 text-primary dark:text-white">Highlights - Seite {currentPage}</h3>

      {currentPageHighlights.length === 0 ? (
        <p className="text-sm text-grey dark:text-grey-dark">Keine Highlights auf dieser Seite. Markieren Sie Text oder halten Sie Alt gedrückt und ziehen Sie, um Highlights zu erstellen.</p>
      ) : (
        <div className="space-y-2">
          {currentPageHighlights.map((highlight) => (
            <HighlightItem
              key={highlight.id}
              highlight={highlight}
              onScrollToHighlight={onScrollToHighlight}
            />
          ))}
        </div>
      )}
    </div>
  );
}
