'use client';

import { Message } from "@/types/auto_generated/message";
import dynamic from "next/dynamic";
import { Suspense } from "react";
// Dynamisch importieren mit ssr: false in einer Client-Komponente
const PDFReader = dynamic(
  () => import("@/components/Document/PDFReader"),
  { ssr: false }
);

interface PDFViewerWrapperProps {
  fileUrl?: string;
  messages?: Message[];
  eventId?: string;
  documentId?: string;
}

export default function PDFViewerWrapper({
  fileUrl,
  messages,
  eventId,
  documentId
}: PDFViewerWrapperProps) {
  
  if (!fileUrl) {
    console.log('Keine PDF-URL verfügbar');
    return (
      <div className="flex h-full w-full items-center justify-center">
        Keine PDF-Datei verfügbar
      </div>
    );
  }

  return (
    <Suspense fallback={<div className="flex h-full w-full items-center justify-center">Dokument wird geladen...</div>}>
        <PDFReader
          fileUrl={fileUrl}
          messages={messages}
          eventId={eventId}
          documentId={documentId}
        />
    </Suspense>
  );
}
