"use client";

import React, { useEffect, useRef, useState } from "react";
import { usePDFContext } from "./PDFContext";
import { FiGrid, FiList } from "react-icons/fi";

interface OutlineItem {
  title: string;
  dest?: any; // Changed from string | null to any to handle different destination formats
  items?: OutlineItem[];
}

export default function PDFSidebar() {
  const {
    pdfDocumentRef,
    highlighterUtilsRef,
    outline,
    setOutline,
    sidebarMode,
    setSidebarMode,
    currentPage,
    totalPages
  } = usePDFContext();
  const [thumbnails, setThumbnails] = useState<string[]>([]);

  // Outline is loaded in PdfLoader component

  // State to track thumbnail generation
  const [thumbnailsInitialized, setThumbnailsInitialized] = useState(false);
  const [visibleThumbnailRange, setVisibleThumbnailRange] = useState<[number, number]>([0, 5]);
  const sidebarRef = useRef<HTMLDivElement>(null);
  const thumbnailGenerationQueue = useRef<number[]>([]);
  const isGeneratingThumbnail = useRef<boolean>(false);

  // Function to generate a single thumbnail
  const generateThumbnail = async (pageIndex: number): Promise<string | null> => {
    if (!pdfDocumentRef.current || !highlighterUtilsRef.current?.getViewer()) return null;

    const pdfDocument = pdfDocumentRef.current;
    const viewer = highlighterUtilsRef.current.getViewer();
    if (!viewer) return null;

    try {
      // Get the page directly from the PDF document
      const page = await pdfDocument.getPage(pageIndex + 1);

      // Create a temporary canvas for the thumbnail
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      if (!context) return null;

      // Set the scale for the thumbnail (smaller than the actual page)
      const viewport = page.getViewport({ scale: 0.2 });
      canvas.width = viewport.width;
      canvas.height = viewport.height;

      // Render the page to the canvas
      await page.render({
        canvasContext: context,
        viewport: viewport
      }).promise;

      // Convert the canvas to a data URL
      return canvas.toDataURL('image/jpeg', 0.7);
    } catch (error) {
      console.error(`Error generating thumbnail for page ${pageIndex + 1}:`, error);
    }

    return null;
  };

  // Process the thumbnail generation queue
  const processQueue = async () => {
    if (isGeneratingThumbnail.current || thumbnailGenerationQueue.current.length === 0) return;

    isGeneratingThumbnail.current = true;
    const pageIndex = thumbnailGenerationQueue.current.shift()!;

    // Skip if thumbnail already exists
    if (thumbnails[pageIndex]) {
      isGeneratingThumbnail.current = false;
      processQueue();
      return;
    }

    const thumbnail = await generateThumbnail(pageIndex);

    if (thumbnail) {
      setThumbnails(prev => {
        const newThumbnails = [...prev];
        newThumbnails[pageIndex] = thumbnail;
        return newThumbnails;
      });
    }

    isGeneratingThumbnail.current = false;
    processQueue();
  };

  // Update the queue when visible range changes
  useEffect(() => {
    if (!thumbnailsInitialized) return;

    // Clear the current queue
    thumbnailGenerationQueue.current = [];

    // Add visible thumbnails to the queue
    const [start, end] = visibleThumbnailRange;
    for (let i = start; i <= end; i++) {
      if (i >= 0 && i < totalPages && !thumbnails[i]) {
        thumbnailGenerationQueue.current.push(i);
      }
    }

    // Start processing the queue
    processQueue();
  }, [visibleThumbnailRange, thumbnailsInitialized, totalPages, thumbnails]);

  // Initialize thumbnails array
  useEffect(() => {
    if (!pdfDocumentRef.current) return;

    const pdfDocument = pdfDocumentRef.current;

    if (!thumbnailsInitialized && pdfDocument.numPages > 0) {
      setThumbnails(Array(pdfDocument.numPages).fill(null));
      setThumbnailsInitialized(true);
    }
  }, [pdfDocumentRef, thumbnailsInitialized]);

  // Handle sidebar scroll to update visible thumbnail range
  useEffect(() => {
    if (!sidebarRef.current || !thumbnailsInitialized || sidebarMode !== 'thumbnails') return;

    const handleSidebarScroll = () => {
      if (!sidebarRef.current) return;

      const container = sidebarRef.current;
      const scrollTop = container.scrollTop;
      const containerHeight = container.clientHeight;

      // Estimate which thumbnails are visible based on scroll position
      // Assuming each thumbnail + margin is about 150px tall
      const thumbnailHeight = 150;
      const startIndex = Math.max(0, Math.floor(scrollTop / thumbnailHeight) - 2);
      const endIndex = Math.min(totalPages - 1, Math.ceil((scrollTop + containerHeight) / thumbnailHeight) + 2);

      setVisibleThumbnailRange([startIndex, endIndex]);
    };

    const sidebarElement = sidebarRef.current;
    sidebarElement.addEventListener('scroll', handleSidebarScroll);

    // Initial check
    handleSidebarScroll();

    return () => {
      sidebarElement.removeEventListener('scroll', handleSidebarScroll);
    };
  }, [sidebarMode, thumbnailsInitialized, totalPages]);

  // Scroll to current page in thumbnail view when page changes
  useEffect(() => {
    if (sidebarMode !== 'thumbnails' || !sidebarRef.current) return;

    // Find the thumbnail element for the current page
    const thumbnailElement = sidebarRef.current.querySelector(`[data-page="${currentPage}"]`);
    if (thumbnailElement) {
      // Scroll the thumbnail into view with some offset
      thumbnailElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, [currentPage, sidebarMode]);

  // Handle click on outline item
  const handleOutlineClick = async (dest: any) => {
    if (!dest || !pdfDocumentRef.current || !highlighterUtilsRef.current?.getViewer()) return;

    try {
      const viewer = highlighterUtilsRef.current.getViewer();
      if (!viewer) return;

      let pageNumber;

      // Handle different destination formats
      if (Array.isArray(dest)) {
        // If dest is already an array (explicit destination)
        try {
          pageNumber = await pdfDocumentRef.current.getPageIndex(dest[0]) + 1;
        } catch (error) {
          console.warn("Could not resolve array destination:", error);
          // Fallback: Try to extract page number from the first element if it's a reference
          if (dest[0] && typeof dest[0] === 'object' && dest[0].num !== undefined) {
            pageNumber = dest[0].num + 1;
          }
        }
      } else if (typeof dest === 'string') {
        // If dest is a string (named destination)
        try {
          const destination = await pdfDocumentRef.current.getDestination(dest);
          if (destination && Array.isArray(destination)) {
            pageNumber = await pdfDocumentRef.current.getPageIndex(destination[0]) + 1;
          }
        } catch (error) {
          console.warn("Could not resolve named destination:", error);
          // No fallback for string destinations
        }
      } else if (typeof dest === 'object') {
        if (dest.num !== undefined) {
          // Direct page reference
          pageNumber = dest.num + 1;
        } else if (dest.gen !== undefined && dest.num !== undefined) {
          // Reference object
          try {
            pageNumber = await pdfDocumentRef.current.getPageIndex({ num: dest.num, gen: dest.gen }) + 1;
          } catch (error) {
            console.warn("Could not resolve reference object:", error);
            pageNumber = dest.num + 1; // Fallback
          }
        } else if (dest.dest) {
          // Nested destination
          return handleOutlineClick(dest.dest);
        } else if (dest.pageNumber !== undefined) {
          // Direct page number
          pageNumber = dest.pageNumber;
        }
      }

      if (pageNumber && pageNumber > 0 && pageNumber <= totalPages) {
        // Scroll to the page
        viewer.scrollPageIntoView({ pageNumber });
      } else {
        console.warn("Invalid page number:", pageNumber);
      }
    } catch (error) {
      console.error("Error navigating to outline destination:", error);
    }
  };

  // Handle click on thumbnail
  const handleThumbnailClick = (pageNumber: number) => {
    if (!highlighterUtilsRef.current?.getViewer()) return;

    const viewer = highlighterUtilsRef.current.getViewer();
    if (!viewer) return;

    viewer.scrollPageIntoView({ pageNumber });
  };

  // Render outline items recursively
  const renderOutlineItems = (items: OutlineItem[] | undefined) => {
    if (!items || items.length === 0) return null;

    return (
      <ul role="list" className="pl-4">
        {items.map((item, index) => (
          <li key={index} className="py-1">
            <button
              onClick={() => handleOutlineClick(item.dest || null)}
              className="text-left hover:text-accent dark:hover:text-accent-dark w-full truncate"
              title={item.title}
              aria-label={`Gehe zu ${item.title}`}
            >
              {item.title || 'Unbenannter Eintrag'}
            </button>
            {item.items && item.items.length > 0 && renderOutlineItems(item.items)}
          </li>
        ))}
      </ul>
    );
  };

  return (
    <div className="h-full flex flex-col">
      <div className="sticky top-0 z-10 bg-white dark:bg-white-dark border-b border-grey-hover dark:border-grey p-2">
        <button
          onClick={() => setSidebarMode(sidebarMode === 'outline' ? 'thumbnails' : 'outline')}
          className="flex items-center justify-start rounded-md p-2 transition-colors bg-grey-hover hover:bg-grey text-grey-dark dark:bg-primary dark:text-grey-hover dark:hover:bg-grey w-full"
          aria-label={sidebarMode === 'outline' ? "Zu Thumbnails wechseln" : "Zu Outline wechseln"}
          title={sidebarMode === 'outline' ? "Zu Thumbnails wechseln" : "Zu Outline wechseln"}
        >
          {sidebarMode === 'outline' ? (
            <>
              <FiList className="h-5 w-5 mr-2" />
              <span>Outline</span>
            </>
          ) : (
            <>
              <FiGrid className="h-5 w-5 mr-2" />
              <span>Thumbnails</span>
            </>
          )}
        </button>
      </div>

      <div ref={sidebarRef} className="overflow-auto flex-grow">
        {sidebarMode === 'outline' ? (
          <div className="outline-container">
            {outline && outline.length > 0 ? (
              renderOutlineItems(outline)
            ) : (
              <p className="text-grey-dark dark:text-grey-hover p-4">Keine Gliederung verfügbar</p>
            )}
          </div>
        ) : (
          <ul role="list" aria-labelledby="thumbnails-heading" className="grid gap-2 p-2">
            <div id="thumbnails-heading" className="sr-only">Seitenvorschau</div>
            {Array.from({ length: totalPages }).map((_, index) => (
              <li key={index} className="thumbnail-item" data-page={index + 1}>
                <button
                  onClick={() => handleThumbnailClick(index + 1)}
                  className={`w-full p-1 rounded ${
                    currentPage === index + 1
                      ? 'ring-2 ring-accent dark:ring-accent-dark'
                      : 'hover:bg-grey-hover dark:hover:bg-grey'
                  }`}
                  aria-label={`Gehe zu Seite ${index + 1}`}
                  title={`Gehe zu Seite ${index + 1}`}
                  data-page={index + 1}
                >
                  <div className="relative pb-[141.4%] w-full overflow-hidden rounded bg-white dark:bg-white-dark">
                    {thumbnails && thumbnails[index] ? (
                      <img
                        src={thumbnails[index]}
                        alt={`Seite ${index + 1}`}
                        className="absolute inset-0 w-full h-full object-contain"
                        loading="lazy"
                      />
                    ) : (
                      <div className="absolute inset-0 flex items-center justify-center text-grey-dark dark:text-grey-hover">
                        Seite {index + 1}
                      </div>
                    )}
                  </div>
                  <div className="text-center text-xs mt-1 text-grey-dark dark:text-grey-hover">
                    {index + 1}
                  </div>
                </button>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
}
