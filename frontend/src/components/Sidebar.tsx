'use client';

import Link from "next/link";
import { usePathname } from "next/navigation";

const menuItems = [
  { name: 'Working Packages', href: '/working-packages' },
  { name: 'Event', href: '/events' },
  { name: 'Status', href: '/status' },
];

export default function Sidebar() {
  const pathname = usePathname();

  return (
    <aside className="w-64 border-r border-grey dark:border-primary bg-grey-hover dark:bg-white-dark h-full basis-2/12">
      <nav className="p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => {
            const isActive = pathname === item.href || pathname.startsWith(`${item.href}/`);

            return (
              <li key={item.name}>
                <Link
                  href={item.href}
                  className={`block rounded-lg px-4 py-2 transition-apple ${
                    isActive
                      ? 'bg-accent text-white dark:bg-accent-dark'
                      : 'text-primary dark:text-white hover:bg-primary-hover dark:hover:bg-white-hover'
                  }`}
                >
                  {item.name}
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>
    </aside>
  );
}
