"use client";

import { FC } from "react";
import { useFormContext } from "react-hook-form";
import { FormStringInput } from "@/components/Form/components/FormStringInput";
import { FormDateTimeInput } from "@/components/Form/components/FormDateTimeInput";
import { FormSelectInput, SelectOption } from "@/components/Form/components/FormSelectInput";
import { FormErrorDisplay } from "@/components/Form/components/FormErrorDisplay";
import { Event } from "@/types/auto_generated/event";

// Form data type using Pick from auto-generated Event type
export type EventFormData = Pick<Event, "name" | "type" | "start" | "end"> & {
  working_packages?: string[]; // documentIds for form handling
};

interface EventFormProps {
  workingPackageOptions: SelectOption[];
  submitButtonText?: string;
}

// Event type options derived from auto-generated type
const eventTypeOptions: SelectOption[] = [
  { value: "Publication", label: "Publication" },
  { value: "Conference", label: "Conference" }
] as const;

export const EventForm: FC<EventFormProps> = ({
  workingPackageOptions,
  submitButtonText = "Event speichern"
}) => {
  const { formState: { errors, isSubmitting, isValid } } = useFormContext<EventFormData>();

  return (
   <>
     <FormErrorDisplay />
      <div className="grid gap-4 md:grid-cols-2">
        <FormStringInput
          name="name"
          label="Event Name"
          required={true}
          className="rounded-lg border border-color-grey p-3 dark:border-color-primary dark:bg-color-primary"
          labelClassName="text-sm font-medium text-color-black dark:text-color-white"
        />

        <FormSelectInput
          name="type"
          label="Event Typ"
          required={true}
          options={eventTypeOptions}
          className="rounded-lg border border-color-grey p-3 dark:border-color-primary dark:bg-color-primary"
          labelClass="text-sm font-medium text-color-black dark:text-color-white"
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <FormDateTimeInput
          name="start"
          label="Startdatum"
          className="rounded-lg border border-color-grey p-3 dark:border-color-primary dark:bg-color-primary"
          labelClass="text-sm font-medium text-color-black dark:text-color-white"
        />

        <FormDateTimeInput
          name="end"
          label="Enddatum"
          minFieldName="start"
          className="rounded-lg border border-color-grey p-3 dark:border-color-primary dark:bg-color-primary"
          labelClass="text-sm font-medium text-color-black dark:text-color-white"
        />
      </div>

      <div>
        <FormSelectInput
          name="working_packages"
          label="Working Packages"
          options={workingPackageOptions}
          multiple={true}
          className="rounded-lg border border-color-grey p-3 dark:border-color-primary dark:bg-color-primary"
          labelClass="text-sm font-medium text-color-black dark:text-color-white"
        />
      </div>

      <div className="flex gap-3 pt-4">
        <button
          type="submit"
          disabled={isSubmitting }
          className={`flex-1 rounded-lg px-6 py-3 font-medium text-color-white border-2 border-primary `}
        >
          {isSubmitting ? "Wird gespeichert..." : submitButtonText}
        </button>
      </div>
    </>
  );
};