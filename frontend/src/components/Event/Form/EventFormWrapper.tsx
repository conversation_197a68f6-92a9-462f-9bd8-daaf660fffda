'use client';

import { FC, useEffect } from "react";
import { useRouter } from "next/navigation";
import { EventForm, EventFormData } from "./EventForm";
import { SelectOption } from "@/components/Form/components/FormSelectInput";
import { getCollection } from "@/utility/strapi/StrapiUtility";
import { ECollectionTypes } from "@/utility/strapi/types";
import { Event } from "@/types/auto_generated/event";
import { FormProvider, useForm } from "react-hook-form";

interface EventFormWrapperProps {
  event?: Event; // Event data from page
  workingPackageOptions: SelectOption[]; // Working package options from page
}

const defaultValues: EventFormData = {
  name: '',
  type: 'Publication',
  start: '',
  end: '',
  working_packages: [],
}

export const EventFormWrapper: FC<EventFormWrapperProps> = ({
  event,
  workingPackageOptions
}) => {
  const router = useRouter();
  const isEditMode = !!event?.documentId;

  const methods = useForm<EventFormData>({
    defaultValues,
  });

  const { reset } = methods;

  // Reset form when event data changes
  useEffect(() => {
    if (event) {
      // Convert event data to form format
      const formData: EventFormData = {
        name: event.name || '',
        type: event.type || 'Publication',
        start: event.start ? new Date(event.start).toISOString().slice(0, 16) : '',
        end: event.end ? new Date(event.end).toISOString().slice(0, 16) : '',
        working_packages: Array.isArray(event.working_packages)
          ? event.working_packages.map(wp => wp.documentId || '').filter(Boolean)
          : [],
      };

      reset(formData);
    } else {
      // Reset to default values for create mode
      reset(defaultValues);
    }
  }, [event, reset]);

  const onSubmit = async (data: EventFormData) => {
    try {
      const eventCollection = getCollection(ECollectionTypes.event);

      // Prepare the data for Strapi
      const eventData = {
        name: data.name,
        type: data.type,
        start: data.start ? new Date(data.start).toISOString() : null,
        end: data.end ? new Date(data.end).toISOString() : null,
        working_packages: data.working_packages && data.working_packages.length > 0
          ? { connect: data.working_packages }
          : undefined
      };

      let result;

      if (isEditMode && event?.documentId) {
        // Update existing event
        result = await eventCollection.update(event.documentId, eventData);
        console.log('Event updated:', result);

        // Redirect to the event's detail page
        router.push(`/events/${event.documentId}`);
      } else {
        // Create new event
        result = await eventCollection.create(eventData);
        console.log('Event created:', result);

        // Redirect to the new event's detail page
        if (result.data?.documentId) {
          router.push(`/events/${result.data.documentId}`);
        } else {
          router.push('/events');
        }
      }

    } catch (error) {
      console.error('Error submitting event:', error);
      // Here you could show an error message to the user
      alert('Fehler beim Speichern des Events. Bitte versuchen Sie es erneut.');
    }
  };

  return (
    <FormProvider {...methods}>
      <div className="max-w-4xl mx-auto p-6 text-white">
        <div className="mb-6">
          <h1 className="text-3xl font-bold ">
            {isEditMode ? 'Event bearbeiten' : 'Neues Event erstellen'}
          </h1>
          <p className="mt-2">
            {isEditMode
              ? 'Bearbeiten Sie die Event-Details und Working Package-Zuordnungen.'
              : 'Erstellen Sie ein neues Event und wählen Sie die zugehörigen Working Packages aus.'
            }
          </p>
        </div>

        <div className="rounded-lg shadow-sm p-6">
          <form onSubmit={methods.handleSubmit(onSubmit)} className="flex flex-col gap-4">
          <EventForm
            workingPackageOptions={workingPackageOptions}
            submitButtonText={isEditMode ? 'Änderungen speichern' : 'Event erstellen'}
          />
          </form>
        </div>
      </div>
    </FormProvider>
  );
};
