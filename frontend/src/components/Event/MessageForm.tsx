'use client';

import { useForm } from "react-hook-form";
import { createMessage } from "@/app/events/[slug]/actions";

interface MessageFormProps {
  eventId: string;
}

type FormData = {
  text: string;
  type: 'Comment' | 'Accept' | 'Deny';
  anonymous_email?: string;
};

export default function MessageForm({ eventId }: MessageFormProps) {
  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { errors, isSubmitting, isSubmitSuccessful },
    setError,
  } = useForm<FormData>({
    defaultValues: {
      text: '',
      type: 'Comment',
      anonymous_email: '',
    },
  });

  const messageType = watch('type');

  const onSubmit = async (data: FormData) => {
    try {
      // Clean up the data
      const formData = {
        ...data,
        anonymous_email: data.anonymous_email && data.anonymous_email.trim() !== ''
          ? data.anonymous_email
          : undefined,
      };

      await createMessage(eventId, formData);
      reset(); // Reset form after successful submission
    } catch (error) {
      console.error('Error submitting message:', error);
      setError('root.serverError', {
        type: 'server',
        message: 'Failed to submit your message. Please try again.'
      });
    }
  };

  return (
    <section className="rounded-lg bg-white p-6 shadow-sm dark:bg-white-dark">
      <h2 className="mb-4 text-xl font-semibold">Add Your Response</h2>

      {isSubmitSuccessful && (
        <div className="mb-4 rounded-lg bg-warning/10 p-4 text-warning dark:bg-warning-dark/20 dark:text-warning-hover">
          Your message has been submitted successfully!
        </div>
      )}

      {errors.root?.serverError && (
        <div className="mb-4 rounded-lg bg-highlight/10 p-4 text-highlight dark:bg-highlight-dark/20 dark:text-highlight-hover">
          {errors.root.serverError.message}
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4">
        <div>
          <label htmlFor="message-type" className="mb-1 block text-sm font-medium">
            Response Type
          </label>
          <div className="flex flex-wrap gap-4">
            <label className="flex items-center gap-2">
              <input
                type="radio"
                value="Comment"
                {...register('type')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500"
              />
              <span>Comment</span>
            </label>
            <label className="flex items-center gap-2">
              <input
                type="radio"
                value="Accept"
                {...register('type')}
                className="h-4 w-4 text-green-600 focus:ring-green-500"
              />
              <span>Accept</span>
            </label>
            <label className="flex items-center gap-2">
              <input
                type="radio"
                value="Deny"
                {...register('type')}
                className="h-4 w-4 text-red-600 focus:ring-red-500"
              />
              <span>Deny</span>
            </label>
          </div>
        </div>

        <div>
          <label htmlFor="anonymous_email" className="mb-1 block text-sm font-medium">
            Your Email (optional)
          </label>
          <input
            id="anonymous_email"
            type="email"
            placeholder="<EMAIL>"
            className="w-full rounded-lg border border-grey p-2 dark:border-grey dark:bg-white-dark"
            {...register('anonymous_email', {
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: 'Invalid email address',
              },
            })}
          />
          {errors.anonymous_email && (
            <p className="mt-1 text-sm text-highlight dark:text-highlight-hover">{errors.anonymous_email.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="text" className="mb-1 block text-sm font-medium">
            Message
          </label>
          <textarea
            id="text"
            rows={4}
            placeholder={
              messageType === 'Accept'
                ? 'Why are you accepting this event?'
                : messageType === 'Deny'
                  ? 'Why are you denying this event?'
                  : 'Enter your comment here...'
            }
            className="w-full rounded-lg border border-grey p-2 dark:border-grey dark:bg-white-dark"
            {...register('text', {
              required: 'Message text is required',
              minLength: {
                value: 3,
                message: 'Message must be at least 3 characters',
              },
            })}
          />
          {errors.text && (
            <p className="mt-1 text-sm text-highlight dark:text-highlight-hover">{errors.text.message}</p>
          )}
        </div>

        <button
          type="submit"
          disabled={isSubmitting}
          className={`mt-2 rounded-lg px-4 py-2 font-medium text-white transition-colors ${
            messageType === 'Accept'
              ? 'bg-warning hover:bg-warning-hover dark:bg-warning-dark dark:hover:bg-warning-hover'
              : messageType === 'Deny'
                ? 'bg-highlight hover:bg-highlight-hover dark:bg-highlight-dark dark:hover:bg-highlight-hover'
                : 'bg-accent hover:bg-accent-hover dark:bg-accent-dark dark:hover:bg-accent-hover'
          } ${isSubmitting ? 'opacity-70 cursor-not-allowed' : ''}`}
        >
          {isSubmitting ? 'Submitting...' : 'Submit Response'}
        </button>
      </form>
    </section>
  );
}
