'use client';

import { Message } from "@/types/auto_generated/message";
import { formatRelativeTime } from "@/utility/date/DateUtility";
import { toArray } from "lodash";

interface MessageListProps {
  messages: Message[];
}

export default function MessageList({ messages }: MessageListProps) {
  const sortedMessages = [...toArray(messages)].sort((a, b) => {
    const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
    const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
    return dateA - dateB;
  });

  return (
    <section className="rounded-lg bg-white p-6 shadow-sm dark:bg-white-dark">
      <h2 className="mb-6 text-xl font-semibold" id="messages">Messages</h2>

      <div className="flex flex-col gap-4" role="log" aria-labelledby="messages">
        {sortedMessages.length === 0 ? (
          <div className="rounded-lg border border-grey p-4 text-center dark:border-primary">
            <p className="text-grey dark:text-grey-hover">No messages yet. Be the first to leave a message!</p>
          </div>
        ) : (
          sortedMessages.map((message, index) => (
            <MessageItem key={index} message={message} />
          ))
        )}
      </div>
    </section>
  );
}

function MessageItem({ message }: { message: Message }) {
  // Determine message type styling
  const getMessageTypeStyles = () => {
    switch (message.type) {
      case 'Accept':
        return 'bg-warning/10 border-warning dark:bg-warning-dark/20 dark:border-warning-dark';
      case 'Deny':
        return 'bg-highlight/10 border-highlight dark:bg-highlight-dark/20 dark:border-highlight-dark';
      case 'Comment':
      default:
        return 'bg-grey-hover border-grey dark:bg-white-dark dark:border-primary';
    }
  };

  // Determine message type badge
  const getMessageTypeBadge = () => {
    switch (message.type) {
      case 'Accept':
        return (
          <span className="rounded-full bg-warning/10 px-2.5 py-0.5 text-xs font-medium text-warning dark:bg-warning-dark/30 dark:text-warning-hover">
            Accepted
          </span>
        );
      case 'Deny':
        return (
          <span className="rounded-full bg-highlight/10 px-2.5 py-0.5 text-xs font-medium text-highlight dark:bg-highlight-dark/30 dark:text-highlight-hover">
            Denied
          </span>
        );
      case 'Comment':
      default:
        return (
          <span className="rounded-full bg-accent/10 px-2.5 py-0.5 text-xs font-medium text-accent dark:bg-accent-dark/30 dark:text-accent-hover">
            Comment
          </span>
        );
    }
  };

  return (
    <div className={`rounded-lg border p-4 ${getMessageTypeStyles()}`}>
      <div className="mb-2 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="font-medium">
            {message.users_permissions_user?.username || message.anonymous_email || 'Anonymous'}
          </span>
          {getMessageTypeBadge()}
        </div>
        <span className="text-xs text-grey dark:text-grey-hover">
          {message.createdAt ? formatRelativeTime(message.createdAt) : ''}
        </span>
      </div>
      <p className="whitespace-pre-wrap">{message.text}</p>
    </div>
  );
}
