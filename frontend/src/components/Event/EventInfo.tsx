'use client';

import { Event } from "@/types/auto_generated/event";
import { formatDate } from "@/utility/date/DateUtility";
import Link from "next/link";
import { toArray } from "lodash";

interface EventInfoProps {
  event: Event;
}

export default function EventInfo({ event }: EventInfoProps) {
  return (
    <section className="rounded-lg  p-6 shadow-sm ">
      <div className="mb-6 flex items-center justify-between w-full text-primary">
        <div className="flex items-center gap-4">
          <h1 className="text-2xl font-bold">{event.name}</h1>
          <span className="rounded-full bg-accent/10 px-3 py-1 text-sm font-medium text-accent dark:bg-accent-dark/30 dark:text-accent-hover">
            {event.type}
          </span>
        </div>
        <Link
          href={`/events/${event.documentId}/edit`}
          className="inline-flex items-center gap-2 rounded-lg px-4 py-2 text-white"
        >
          Event bearbeiten
        </Link>
      </div>

      <div className="mb-6 grid gap-4 sm:grid-cols-2">
        <div>
          <h2 className="text-sm font-medium text-grey dark:text-grey-dark">Start Date</h2>
          <p className="text-lg">{event.start ? formatDate(event.start) : 'Not specified'}</p>
        </div>
        <div>
          <h2 className="text-sm font-medium text-grey dark:text-grey-dark">End Date</h2>
          <p className="text-lg">{event.end ? formatDate(event.end) : 'Not specified'}</p>
        </div>
      </div>

      <div>
        <h2 className="mb-3 text-lg font-semibold text-white" id="working-packages">Working Packages</h2>
        <ul className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3" role="list" aria-labelledby="working-packages">
          {toArray(event.working_packages).map((wp, index) => (
            <li key={index} className="rounded-lg border border-grey p-3 dark:border-primary">
              <Link href={`/working-packages/${wp.documentId}`} className="font-medium hover:text-accent dark:hover:text-accent-hover">
                {wp.name}
              </Link>
              <p className="mt-1 text-sm text-grey dark:text-grey-dark">
                {toArray(wp.users_permissions_users).length} users
              </p>
            </li>
          ))}
          {toArray(event.working_packages).length === 0 && (
            <li className="col-span-full text-grey dark:text-grey-dark">No working packages associated with this event.</li>
          )}
        </ul>
      </div>
    </section>
  );
}
