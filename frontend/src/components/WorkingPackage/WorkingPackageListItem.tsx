import { WorkingPackage } from "@/types/auto_generated/workingPackage";
import { toArray } from "lodash";
import { sanitizeId } from "@/utility/accessability/AccessabilityUtility";
import Link from "next/link";
import UserListItem from "@/components/User/UserListItem";


export default function WorkingPackageListItem(
  item: WorkingPackage
) {

  const id = sanitizeId(item.documentId);

  return (
    <li className={"flex flex-col gap-2 "}>
      <details>
        <summary className={"bg-primary rounded-md px-4 py-2 text-white flex gap-2"}>
          {item.name}

          <Link href={`/working-packages/${item.documentId}`} className={"ml-auto"} >
            Details
          </Link>
        </summary>

        <div id={`${id}-relevant`}>Re<PERSON><PERSON></div>
        <ul role={"list"} aria-labelledby={`${id}-relevant`} className={"list-inside list-disc pl-4"}>
          {toArray(item.users_permissions_users).map((user, index) => {
            return <UserListItem key={index} {...user} />;
          })}
        </ul>

      </details>
    </li>
  );
}