{"name": "esploro-one", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "copy-pdf-worker": "cp node_modules/pdfjs-dist/build/pdf.worker.min.mjs public/pdf.worker4.js", "dev:with-worker": "yarn copy-pdf-worker && next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prettier": "prettier --write ."}, "dependencies": {"@strapi/client": "1.2.0", "fabric": "6.6.4", "lodash": "4.17.21", "next": "15.3.1", "pdfjs-dist": "5.2.133", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "7.56.2", "react-rnd": "10.5.2", "ts-debounce": "4.0.0", "react-icons": "^5.5.0", "emoji-regex": "^10.4.0"}, "devDependencies": {"@eslint/eslintrc": "3.3.1", "@tailwindcss/postcss": "4.1.4", "@types/lodash": "4.17.16", "@types/node": "22.14.1", "@types/react": "19.1.2", "@types/react-dom": "19.1.2", "eslint": "9.25.1", "eslint-config-prettier": "10.1.2", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "0.6.11", "tailwindcss": "4.1.4", "typescript": "5.8.3"}}